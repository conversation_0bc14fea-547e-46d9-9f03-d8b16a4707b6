/*-- -------------------------- -->
<---      Calculator Section    -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #calculator-section {
        padding: var(--sectionPadding);
        background-color: #fff;
        position: relative;

        .cs-container {
            width: 100%;
            max-width: calc(1280 / 16 * 1rem);
            margin: auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: clamp(3rem, 6vw, 4rem);
        }

        .cs-tab-nav {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: calc(8 / 16 * 1rem);
            margin-bottom: calc(32 / 16 * 1rem);
        }

        .cs-tab-track {
            display: flex;
            gap: calc(8 / 16 * 1rem);
            flex-wrap: wrap;
            justify-content: center;
        }

        .cs-tab-button {
            /* 14px - 16px */
            font-size: clamp(0.875rem, 1.5vw, 1rem);
            line-height: 1.2em;
            text-decoration: none;
            font-weight: 700;
            text-align: center;
            margin: 0;
            color: var(--bodyTextColor);
            min-width: calc(150 / 16 * 1rem);
            padding: calc(12 / 16 * 1rem) calc(24 / 16 * 1rem);
            background-color: transparent;
            border: 2px solid var(--primary);
            border-radius: calc(8 / 16 * 1rem);
            display: inline-block;
            position: relative;
            z-index: 1;
            /* prevents padding from affecting height and width */
            box-sizing: border-box;
            transition: color 0.3s, background-color 0.3s, border-color 0.3s;
            cursor: pointer;

            &:before {
                content: '';
                position: absolute;
                height: 100%;
                width: 0%;
                background: var(--primary);
                opacity: 1;
                top: 0;
                left: 0;
                z-index: -1;
                border-radius: calc(6 / 16 * 1rem);
                transition: width 0.3s;
            }

            &:hover {
                color: #fff;
                &:before {
                    width: 100%;
                }
            }

            &.active {
                color: #fff;
                background-color: var(--primary);
                border-color: var(--primary);

                &:before {
                    width: 100%;
                }
            }
        }

        .cs-calculator-container {
            width: 100%;
            position: relative;
        }

        .cs-calculator {
            width: 100%;
            background-color: #f7f7f7;
            border-radius: calc(16 / 16 * 1rem);
            padding: clamp(2rem, 5vw, 3rem);
            display: none;

            &.active {
                display: block;
            }
        }

        .cs-calculator-title {
            /* 31px - 39px */
            font-size: clamp(1.9375rem, 3.9vw, 2.4375rem);
            font-weight: 900;
            line-height: 1.2em;
            text-align: center;
            max-width: 43.75rem;
            margin: 0 auto calc(16 / 16 * 1rem);
            color: var(--headerColor);
            position: relative;
        }

        .cs-calculator-description {
            font-size: calc(16 / 16 * 1rem);
            line-height: 1.5em;
            text-align: center;
            width: 100%;
            max-width: calc(630 / 16 * 1rem);
            margin: 0 auto calc(32 / 16 * 1rem);
            color: var(--bodyTextColor);
        }

        .cs-calculator-form {
            width: 100%;
            max-width: calc(630 / 16 * 1rem);
            margin: 0 auto calc(32 / 16 * 1rem);
            display: flex;
            flex-direction: column;
            gap: calc(16 / 16 * 1rem);
        }

        .cs-input-group {
            display: flex;
            flex-direction: column;
            gap: calc(8 / 16 * 1rem);

            label {
                font-size: calc(16 / 16 * 1rem);
                font-weight: 700;
                line-height: 1.5em;
                color: var(--headerColor);
                display: block;
            }

            input,
            select {
                font-size: calc(16 / 16 * 1rem);
                width: 100%;
                height: calc(56 / 16 * 1rem);
                padding: 0 calc(24 / 16 * 1rem);
                background-color: #fff;
                color: var(--headerColor);
                border: 1px solid #dadada;
                border-radius: calc(8 / 16 * 1rem);
                /* prevents padding from affecting height and width */
                box-sizing: border-box;
                transition: border-color 0.3s;

                &:focus {
                    outline: none;
                    border-color: var(--primary);
                }

                &::placeholder {
                    color: #b4b2c7;
                    opacity: 1;
                }

                &[readonly] {
                    background-color: #f5f5f5;
                    color: var(--bodyTextColor);
                }
            }
        }

        .cs-calculate-btn {
            font-size: calc(16 / 16 * 1rem);
            /* 46px - 56px */
            line-height: clamp(2.875rem, 5.5vw, 3.5rem);
            text-decoration: none;
            font-weight: 700;
            text-align: center;
            margin: calc(16 / 16 * 1rem) auto 0;
            color: #fff;
            min-width: calc(200 / 16 * 1rem);
            padding: 0 calc(24 / 16 * 1rem);
            background-color: var(--primary);
            border-radius: calc(8 / 16 * 1rem);
            border: none;
            display: inline-block;
            position: relative;
            z-index: 1;
            /* prevents padding from affecting height and width */
            box-sizing: border-box;
            transition: color 0.3s, background-color 0.3s;
            cursor: pointer;

            &:before {
                content: '';
                position: absolute;
                height: 100%;
                width: 0%;
                background: #000;
                opacity: 1;
                top: 0;
                left: 0;
                z-index: -1;
                border-radius: calc(8 / 16 * 1rem);
                transition: width 0.3s;
            }

            &:hover {
                &:before {
                    width: 100%;
                }
            }
        }

        .cs-results {
            width: 100%;
            max-width: calc(630 / 16 * 1rem);
            margin: 0 auto;
            background-color: #fff;
            border-radius: calc(12 / 16 * 1rem);
            padding: calc(24 / 16 * 1rem);
            border: 1px solid #e8e8e8;

            h3 {
                font-size: calc(20 / 16 * 1rem);
                font-weight: 700;
                line-height: 1.2em;
                margin: 0 0 calc(16 / 16 * 1rem);
                color: var(--headerColor);
                text-align: center;
            }
        }

        .cs-result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: calc(12 / 16 * 1rem) 0;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
                border-bottom: none;
            }

            .cs-result-label {
                font-size: calc(16 / 16 * 1rem);
                font-weight: 600;
                color: var(--headerColor);
            }

            .cs-result-value {
                font-size: calc(16 / 16 * 1rem);
                font-weight: 700;
                color: var(--primary);
                text-align: right;
            }
        }
    }
}

/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
    #calculator-section {
        .cs-tab-nav {
            gap: calc(12 / 16 * 1rem);
        }

        .cs-tab-button {
            min-width: calc(180 / 16 * 1rem);
            padding: calc(16 / 16 * 1rem) calc(32 / 16 * 1rem);
        }

        .cs-calculator-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: calc(24 / 16 * 1rem);

            .cs-calculate-btn {
                grid-column: 1 / -1;
                justify-self: center;
            }
        }

        .cs-results {
            padding: calc(32 / 16 * 1rem);
        }

        .cs-result-item {
            padding: calc(16 / 16 * 1rem) 0;

            .cs-result-label,
            .cs-result-value {
                font-size: calc(18 / 16 * 1rem);
            }
        }
    }
}

/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
    #calculator-section {
        .cs-container {
            overflow: visible;
        }

        .cs-tab-nav {
            flex-wrap: nowrap;
            overflow-x: hidden;
            overflow-y: hidden;
            padding: 0;
            margin-bottom: calc(32 / 16 * 1rem);
            position: relative;

            /* Hide scrollbar */
            &::-webkit-scrollbar {
                display: none;
            }

            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        .cs-tab-track {
            display: flex;
            gap: calc(16 / 16 * 1rem);
            flex-wrap: nowrap;
            justify-content: flex-start;
            animation: infiniteScroll 60s linear infinite;
            width: max-content;

            &:hover {
                animation-play-state: paused;
            }
        }

        .cs-tab-button {
            min-width: calc(180 / 16 * 1rem);
            white-space: nowrap;
            flex-shrink: 0;
        }

        .cs-calculator {
            padding: clamp(3rem, 6vw, 4rem);
        }

        .cs-calculator-form {
            grid-template-columns: 1fr 1fr 1fr;
            gap: calc(32 / 16 * 1rem);

            .cs-calculate-btn {
                grid-column: 1 / -1;
                max-width: calc(300 / 16 * 1rem);
            }
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        #calculator-section {
            background-color: var(--dark);

            .cs-calculator {
                background-color: var(--medium);
            }

            .cs-calculator-title {
                color: var(--bodyTextColorWhite);
            }

            .cs-calculator-description {
                color: var(--bodyTextColorWhite);
            }

            .cs-input-group {
                label {
                    color: var(--bodyTextColorWhite);
                }

                input,
                select {
                    background-color: var(--dark);
                    color: var(--bodyTextColorWhite);
                    border-color: var(--accent);

                    &:focus {
                        border-color: var(--primary);
                    }

                    &::placeholder {
                        color: #8e8e8e;
                    }

                    &[readonly] {
                        background-color: var(--accent);
                        color: var(--bodyTextColorWhite);
                    }
                }
            }

            .cs-results {
                background-color: var(--dark);
                border-color: var(--accent);

                h3 {
                    color: var(--bodyTextColorWhite);
                }
            }

            .cs-result-item {
                border-bottom-color: var(--accent);

                .cs-result-label {
                    color: var(--bodyTextColorWhite);
                }

                .cs-result-value {
                    color: var(--primary);
                }
            }
        }
    }
}

/* Infinite scroll animation */
@keyframes infiniteScroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}