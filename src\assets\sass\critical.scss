/*-- -------------------------- -->
<---           Hero             -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #hero-1467 {
        padding: clamp(13.5rem, 22.95vw, 16.875em) calc(16 / 16 * 1rem) clamp(6.25rem, 7vw, 12.5rem);
        position: relative;
        z-index: 1;

        .cs-container {
            width: 100%;
            /* changes to 1280px at desktop */
            max-width: calc(704 / 16 * 1rem);
            margin: auto;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            gap: calc(48 / 16 * 1rem);
        }

        .cs-content {
            text-align: center;
            width: 100%;
            max-width: calc(570 / 16 * 1rem);
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            flex-direction: column;
            align-items: center;
        }

        .cs-flex {
            text-align: center;
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            flex-direction: column;
            align-items: center;
        }

        .cs-title {
            /* 39px - 49px */
            font-size: clamp(2.4375rem, 6.4vw, 3.0625rem);
            font-weight: 700;
            line-height: 1.2em;
            text-align: inherit;
            max-width: calc(829 / 16 * 1rem);
            /* 16px - 24px */
            margin: 0 auto clamp(1rem, 4vw, 1.5rem) 0;
            color: #fff;
            position: relative;
            z-index: 1;

            &:before {
                content: '';
                width: calc(60 / 16 * 1rem);
                height: 8px;
                margin: 0 auto calc(32 / 16 * 1rem);
                background: var(--primary);
                border-radius: 8px;
                opacity: 1;
                display: block;
            }
        }

        .cs-text {
            /* 16px - 20px */
            font-size: clamp(1rem, 1.95vw, 1.25rem);
            line-height: 1.5em;
            text-align: inherit;
            width: 100%;
            /* 464px - 622px */
            max-width: clamp(29rem, 60vw, 38.785rem);
            margin: 0;
            margin-bottom: calc(32 / 16 * 1rem);
            color: #fff;
        }

        .cs-button-solid {
            font-size: calc(16 / 16 * 1rem);
            /* 46px - 56px */
            line-height: clamp(2.875rem, 5.5vw, 3.5rem);
            text-decoration: none;
            font-weight: 700;
            text-align: center;
            margin: 0;
            color: #fff;
            min-width: calc(200 / 16 * 1rem);
            padding: 0 calc(24 / 16 * 1rem);
            background-color: var(--primary);
            border-radius: calc(50 / 16 * 1rem);
            overflow: hidden;
            display: inline-block;
            position: relative;
            z-index: 1;
            /* prevents padding from adding to the width */
            box-sizing: border-box;

            &:before {
                content: "";
                position: absolute;
                height: 100%;
                width: 0%;
                background: #000;
                opacity: 1;
                top: 0;
                left: 0;
                z-index: -1;
                transition: width 0.3s;
            }

            &:hover {
                &:before {
                    width: 100%;
                }
            }
        }

        .cs-reviews {
            /* 40px - 80px */
            margin-top: clamp(2.5rem, 6vw, 5rem);
            /* prevents padding and border from affecting height and width */
            box-sizing: border-box;
            border-radius: calc(12 / 16 * 1rem);
            box-shadow: 0px 8px 100px 0px rgba(0, 0, 0, 0.08);
            display: inline-flex;
            justify-content: flex-start;
            align-items: flex-start;
            flex-direction: column;
            /* 8px - 16px */
            gap: clamp(0.5rem, 2vw, 1rem);
            position: relative;
            z-index: -1;
        }

        .cs-reviews-flex {
            margin-bottom: calc(16 / 16 * 1rem);
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }

        .cs-stars {
            margin: 0 0 calc(8 / 16 * 1rem) 0;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 2px;
        }

        .cs-icon {
            width: calc(16 / 16 * 1rem);
            height: auto;
        }

        .cs-desc {
            font-size: var(--bodyFontSize);
            line-height: 1.5em;
            text-align: inherit;
            max-width: 20ch;
            margin: 0;
            color: #f7f7f7;
        }

        .cs-reviews-img {
            width: calc(48 / 16 * 1rem);
            height: auto;
            margin: 0 calc(-16 / 16 * 1rem) 0 0;
            border-radius: 50%;
            border: 1px solid #fff;
            background-color: #fff;
        }

        .cs-reviews-number {
            font-size: calc(16 / 16 * 1rem);
            font-weight: 700;
            width: calc(48 / 16 * 1rem);
            height: calc(48 / 16 * 1rem);
            background-color: var(--secondary);
            color: var(--bodyTextColorWhite);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .cs-right {
            text-align: center;
            width: 100%;
            /* 16px - 48px left & right */
            /* 32px - 48px top & bottom */
            padding: clamp(2rem, 5vw, 3rem) clamp(1rem, 4vw, 3rem);
            box-sizing: border-box;
            border-radius: calc(24 / 16 * 1rem);
            border-top: 8px solid var(--primary);
            background-color: #fff;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .cs-tag {
            font-size: calc(12 / 16 * 1rem);
            text-transform: uppercase;
            font-weight: 700;
            letter-spacing: .1em;
            line-height: calc(28 / 16 * 1rem);
            padding: 0 calc(32 / 16 * 1rem) 0 calc(28 / 16 * 1rem);
            overflow: hidden;
            color: var(--bodyTextColorWhite);
            border-radius: calc(40 / 16 * 1rem);
            display: inline-block;
            position: absolute;
            top: calc(8 / 16 * 1rem);
            right: 0;
            z-index: 1;
            transform: rotate(14deg);
            transform-origin: right;

            &:before {
                /* background color */
                content: '';
                height: 100%;
                background: var(--secondary);
                opacity: 1;
                position: absolute;
                display: block;
                top: 0;
                left: 0;
                right: 20px;
                z-index: -1;
            }

            &:after {
                /* triangle */
                content: '';
                width: calc(25 / 16 * 1rem);
                height: calc(25 / 16 * 1rem);
                background-color: var(--secondary);
                opacity: 1;
                overflow: hidden;
                position: absolute;
                display: block;
                top: 50%;
                right: calc(6 / 16 * 1rem);
                z-index: -1;
                transform: translateY(-50%) rotate(45deg);
            }
        }

        .cs-dot {
            width: calc(8 / 16 * 1rem);
            height: calc(8 / 16 * 1rem);
            background-color: #fff;
            border-radius: 50%;
            position: absolute;
            right: calc(15 / 16 * 1rem);
            top: 50%;
            transform: translateY(-50%);
        }

        .cs-h2 {
            /* 31px - 39px */
            font-size: clamp(1.9375rem, 4vw, 2.4375rem);
            font-weight: 900;
            text-align: inherit;
            max-width: 20ch;
            margin: 0 0 calc(8 / 16 * 1rem) 0;
            color: var(--headerColor);
        }

        .cs-expires {
            font-size: calc(16 / 16 * 1rem);
            line-height: 1.5em;
            text-align: inherit;
            margin: 0 0 calc(16 / 16 * 1rem) 0;
            color: var(--secondary);
            display: block;
        }

        .cs-form-text {
            font-size: var(--bodyFontSize);
            line-height: 1.5em;
            text-align: inherit;
            width: 100%;
            max-width: calc(650 / 16 * 1rem);
            margin: 0 0 calc(24 / 16 * 1rem) 0;
            color: var(--bodyTextColor);
        }

        .cs-form {
            width: 100%;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            align-items: center;
            gap: calc(16 / 16 * 1rem);
        }

        .cs-label {
            font-size: calc(14 / 16 * 1rem);
            width: 100%;
            color: #767676;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            gap: calc(4 / 16 * 1rem);
        }

        .cs-input,
        select {
            font-size: calc(16 / 16 * 1rem);
            width: 100%;
            height: calc(56 / 16 * 1rem);
            padding: 0;
            padding-left: calc(24 / 16 * 1rem);
            color: var(--bodyTextColor);
            background-color: #f7f7f7;
            border: none;
            border-radius: calc(8 / 16 * 1rem);
            /* prevents padding from adding to height and width */
            box-sizing: border-box;
            transition: border-color 0.3s;

            &:hover {
                border-color: var(--secondary);
            }

            &:focus {
                outline: 1px solid var(--secondary);
            }

            &::placeholder {
                color: #7d799c;
                opacity: 0.6;
            }
        }

        .cs-textarea {
            min-height: calc(95 / 16 * 1rem);
            padding-top: calc(24 / 16 * 1rem);
            margin-bottom: calc(12 / 16 * 1rem);
            font-family: inherit;
        }

        .cs-submit {
            text-transform: uppercase;
            width: 100%;
            margin: 0 0 calc(16 / 16 * 1rem) 0;
            border: none;

            &:hover {
                cursor: pointer;
            }
        }

        .cs-disclaimer {
            font-size: calc(14 / 16 * 1rem);
            text-align: left;
            line-height: 1.5em;
            margin: 0 auto 0 0;
            color: var(--bodyTextColor);
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            gap: calc(4 / 16 * 1rem);
        }

        .cs-disclaimer-icon {
            width: calc(20 / 16 * 1rem);
            height: auto;
        }

        .cs-background {
            width: 100%;
            height: 100%;
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            z-index: -2;

            &:before {
                /* Overlay */
                content: "";
                width: 100%;
                height: 100%;
                background: #1a1a1a;
                opacity: 0.88;
                position: absolute;
                display: block;
                top: 0;
                left: 0;
                z-index: 1;
                /* prevents the cursor from interacting with it */
                pointer-events: none;
            }

            img {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                object-fit: cover;
            }
        }
    }
}

/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
    #hero-1467 {
        .cs-form {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
        }

        .cs-label {
            grid-column: span 12;

            &:nth-of-type(1),
            &:nth-of-type(2),
            &:nth-of-type(3),
            &:nth-of-type(4) {
                grid-column: span 6;
            }
        }

        .cs-submit {
            grid-column: span 12;
        }
    }
}

/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
    #hero-1467 {
        .cs-container {
            max-width: calc(1280 / 16 * 1rem);
            flex-direction: row;
            flex-wrap: nowrap;
            justify-content: space-between;
        }

        .cs-right {
            align-items: flex-start;
        }

        .cs-title {
            &:before {
                margin-left: 0;
            }
        }

        .cs-content {
            text-align: left;
            align-items: flex-start;
        }

        .cs-flex {
            text-align: left;
            align-items: flex-start;
        }
    }
}

/* Desktop Parallax Effect - 1600px */
@media only screen and (min-width: 100rem) {
    #hero-1467 {
        background: url("/assets/images/hero.webp");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        background-attachment: fixed;

        .cs-background {
            img {
                display: none;
            }
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        #hero-1467 {
            .cs-right {
                background-color: var(--medium);
            }

            .cs-h2,
            .cs-form-text,
            .cs-label,
            .cs-disclaimer {
                color: var(--bodyTextColorWhite);
            }

            .cs-disclaimer-icon {
                /* turns it white */
                filter: grayscale(1) brightness(1000%);
            }

            .cs-input,
            select {
                background-color: rgba(255, 255, 255, .1);
                color: var(--bodyTextColorWhite);
            }

            .cs-form-text {
                opacity: .8;
            }
        }
    }
}