/* CODESTITCH STYLES, RESET, HEADER/NAVIGATION AND FOOTER */
/*-- -------------------------- -->
<---        Core Styles         -->
<--- -------------------------- -*/
/* CodeStitch Root and Helpers */
@media only screen and (min-width: 0rem) {
  :root {
    --primary: #3498db;
    --primaryLight: #3498db;
    --secondary: #3498db;
    --secondaryLight: #3498db;
    --headerColor: #1a1a1a;
    --bodyTextColor: #4e4b66;
    --bodyTextColorWhite: #fafbfc;
    /* 13px - 16px */
    --topperFontSize: clamp(0.8125rem, 1.6vw, 1rem);
    /* 31px - 49px */
    --headerFontSize: clamp(1.9375rem, 3.9vw, 3.0625rem);
    --bodyFontSize: 1rem;
    /* 60px - 100px top and bottom */
    --sectionPadding: clamp(3.75rem, 7.82vw, 6.25rem) 1rem;
  }
  .cs-topper {
    display: block;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-size: var(--topperFontSize);
    line-height: 1.2em;
    font-weight: 700;
    color: var(--primary);
    text-align: inherit;
  }
  .cs-title {
    position: relative;
    margin: 0 0 1rem 0;
    max-width: 43.75rem;
    font-size: var(--headerFontSize);
    line-height: 1.2em;
    font-weight: 900;
    color: var(--headerColor);
    text-align: inherit;
  }
  .cs-text {
    margin: 0;
    max-width: 40.625rem;
    width: 100%;
    font-size: var(--bodyFontSize);
    line-height: 1.5em;
    color: var(--bodyTextColor);
    text-align: inherit;
  }
  .cs-button-solid {
    z-index: 1;
    position: relative;
    display: inline-block;
    background-color: var(--primary);
    width: auto;
    padding: 0 1.875rem;
    text-decoration: none;
    text-transform: uppercase;
    font-size: 1rem;
    line-height: 3.125em;
    font-weight: bold;
    color: #000;
    transition: color 0.3s;
    transition-delay: 0.1s;
    text-align: center;
  }
  .cs-button-solid:hover {
    color: #fff;
  }
  .cs-button-solid:hover:before {
    width: 100%;
  }
  .cs-button-solid:before {
    z-index: -1;
    position: absolute;
    top: 0;
    left: 0;
    content: "";
    opacity: 1;
    display: block;
    background-color: #000;
    height: 100%;
    width: 0;
    transition: width 0.3s;
  }
  .cs-hide-on-mobile {
    display: none;
  }
}
/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
  .cs-hide-on-mobile {
    display: block;
  }
  .cs-hide-on-desktop {
    display: none;
  }
}
/* Fonts and general styles */
@media only screen and (min-width: 0rem) {
  body,
  html {
    margin: 0;
    overflow-x: hidden;
    padding: 0;
    font-family: "Maven Pro", Arial, sans-serif;
    font-size: 100%;
    color: var(--bodyTextColor);
  }
  *,
  *:before,
  *:after {
    margin: 0;
    box-sizing: border-box;
    padding: 0;
  }
  body {
    transition: background-color 0.3s;
  }
  .container {
    position: relative;
    margin: auto;
    width: 92%;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin: 0;
    color: var(--headerColor);
  }
  p,
  li,
  a {
    margin: 0;
    font-size: 1rem;
    line-height: 1.5em;
  }
  p,
  li {
    color: #353535;
  }
  a:hover,
  button:hover {
    cursor: pointer;
  }
  .skip {
    z-index: -1111111;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
  }
  /* maven-pro-regular - latin */
  @font-face {
    font-display: swap;
    /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: "Maven Pro";
    font-style: normal;
    font-weight: 400;
    src: url("../fonts/maven-pro-v39-latin-regular.woff2") format("woff2");
    /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  /* maven-pro-700 - latin */
  @font-face {
    font-display: swap;
    /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: "Maven Pro";
    font-style: normal;
    font-weight: 700;
    src: url("../fonts/maven-pro-v39-latin-700.woff2") format("woff2");
    /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  /* maven-pro-900 - latin */
  @font-face {
    font-display: swap;
    /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: "Maven Pro";
    font-style: normal;
    font-weight: 900;
    src: url("../fonts/maven-pro-v39-latin-900.woff2") format("woff2");
    /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
}
/* Reset Margins */
@media only screen and (min-width: 1024px) {
  body,
  html {
    margin: 0;
    padding: 0;
  }
}
/* Scale full website with the viewport width */
@media only screen and (min-width: 3000px) {
  body,
  html {
    font-size: 0.55vw;
  }
}
/*-- -------------------------- -->
<---      Dark Mode Toggle      -->
<--- -------------------------- -*/
/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  body.dark-mode #dark-mode-toggle .cs-sun {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
  body.dark-mode #dark-mode-toggle .cs-moon {
    opacity: 0;
    transform: translate(-50%, -150%);
  }
  #dark-mode-toggle {
    width: 3rem;
    height: 3rem;
    padding: 0;
    background: transparent;
    border: none;
    display: block;
    position: absolute;
    right: 5.625rem;
    bottom: 1rem;
    z-index: 1000;
    overflow: hidden;
  }
  #dark-mode-toggle img,
  #dark-mode-toggle svg {
    width: 1.25rem;
    height: 1.25rem;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
  }
  #dark-mode-toggle .cs-moon {
    z-index: 2;
    transition: transform 0.3s, opacity 0.3s;
    fill: #1a1a1a;
  }
  #dark-mode-toggle .cs-sun {
    opacity: 0;
    z-index: 1;
    transform: translate(-50%, 100%);
    transition: transform 0.3s, opacity 0.3s;
  }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #dark-mode-toggle {
    margin-left: 1rem;
    order: 3;
    position: relative;
    right: auto;
    bottom: auto;
  }
}
/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
  #dark-mode-toggle {
    margin: 0;
    position: relative;
    right: auto;
    bottom: auto;
    transform: none;
  }
  #dark-mode-toggle:hover {
    cursor: pointer;
  }
}
/*-- -------------------------- -->
<---     Mobile Navigation      -->
<--- -------------------------- -*/
/* Mobile - 1023px */
@media only screen and (max-width: 1023px) {
  body.cs-open {
    overflow: hidden;
  }
  body.scroll #cs-navigation {
    /* 53px, same height as the cs-top-container */
    transform: translateY(-3.3125rem);
  }
  #cs-navigation {
    width: 100%;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    background-color: #fff;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    position: fixed;
    z-index: 10000;
    transition: transform 0.3s;
  }
  #cs-navigation:before {
    content: "";
    width: 100%;
    height: 0vh;
    background: rgba(0, 0, 0, 0.6);
    opacity: 0;
    display: block;
    position: absolute;
    top: 100%;
    right: 0;
    z-index: -1100;
    transition: height 0.5s, opacity 0.5s;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
  }
  #cs-navigation.cs-active:before {
    height: 150vh;
    opacity: 1;
  }
  #cs-navigation.cs-active .cs-ul-wrapper {
    opacity: 1;
    transform: scaleY(1);
    transition-delay: 0.15s;
  }
  #cs-navigation.cs-active .cs-li {
    opacity: 1;
    transform: translateY(0);
  }
  #cs-navigation .cs-top-bar {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  #cs-navigation .cs-top-container {
    width: 100%;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    padding: 1rem 1.5rem;
    background-color: #f7f7f7;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
  }
  #cs-navigation .cs-top-contact {
    width: auto;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    /* 16px - 24px */
    gap: clamp(1rem, 2vw, 1.5rem);
  }
  #cs-navigation .cs-top-link {
    font-size: 0.875rem;
    line-height: 1.5em;
    text-decoration: none;
    margin: 0;
    color: var(--bodyTextColor);
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 0.5rem;
    position: relative;
  }
  #cs-navigation .cs-top-link:nth-of-type(2) {
    display: none;
  }
  #cs-navigation .cs-link-icon {
    width: 1rem;
    height: auto;
    display: block;
  }
  #cs-navigation .cs-top-social {
    opacity: 1;
    display: flex;
    visibility: visible;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    transition: opacity 0.3s, visibility 0.3s, height 0.3s;
  }
  #cs-navigation .cs-social-link {
    text-decoration: none;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
  }
  #cs-navigation .cs-social-icon {
    width: 1.25rem;
    height: auto;
    display: block;
  }
  #cs-navigation .cs-container {
    width: 100%;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    padding: 1.25rem 1rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: relative;
  }
  #cs-navigation .cs-logo {
    width: auto;
    height: 2.5rem;
    margin: 0 auto 0 0;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    order: 1;
    z-index: 10;
  }
  #cs-navigation .cs-logo img {
    width: auto;
    height: 100%;
    /* ensures the image never overflows the container. It stays contained within it's width and height and expands to fill it then stops once it reaches an edge */
    object-fit: contain;
  }
  #cs-navigation .cs-nav {
    order: 2;
  }
  #cs-navigation .cs-toggle {
    width: 2.875rem;
    height: 2.875rem;
    margin: 0 0 0 auto;
    background-color: var(--primary);
    border: none;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform 0.6s;
  }
  #cs-navigation .cs-toggle.cs-active {
    transform: rotate(180deg);
  }
  #cs-navigation .cs-active .cs-line1 {
    top: 50%;
    transform: translate(-50%, -50%) rotate(225deg);
  }
  #cs-navigation .cs-active .cs-line2 {
    top: 50%;
    transform: translate(-50%, -50%) translateY(0) rotate(-225deg);
    transform-origin: center;
  }
  #cs-navigation .cs-active .cs-line3 {
    opacity: 0;
    bottom: 100%;
  }
  #cs-navigation .cs-box {
    /* 24px - 28px */
    width: clamp(1.5rem, 2vw, 1.75rem);
    height: 0.75rem;
    position: relative;
  }
  #cs-navigation .cs-line {
    width: 100%;
    height: 2px;
    background-color: #FAFBFC;
    border-radius: 2px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  #cs-navigation .cs-line1 {
    top: 0;
    transition: transform 0.5s, top 0.3S, left 0.3S;
    animation-duration: 0.7s;
    animation-timing-function: ease;
    animation-direction: normal;
    animation-fill-mode: forwards;
    transform-origin: center;
  }
  #cs-navigation .cs-line2 {
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    transition: top 0.3s, left 0.3s, transform 0.5s;
    animation-duration: 0.7s;
    animation-timing-function: ease;
    animation-direction: normal;
    animation-fill-mode: forwards;
  }
  #cs-navigation .cs-line3 {
    bottom: 0;
    transition: bottom 0.3s, opacity 0.3s;
  }
  #cs-navigation .cs-ul-wrapper {
    width: 100%;
    height: auto;
    padding-bottom: 3rem;
    background-color: #fff;
    box-shadow: inset rgba(0, 0, 0, 0.2) 0px 8px 24px;
    opacity: 0;
    position: absolute;
    top: 100%;
    left: 0;
    z-index: -1;
    overflow: hidden;
    transform: scaleY(0);
    transition: transform 0.4s, opacity 0.3s;
    transform-origin: top;
  }
  #cs-navigation .cs-ul {
    width: 100%;
    height: auto;
    max-height: 65vh;
    margin: 0;
    padding: 3rem 0 0 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 1.25rem;
    overflow: scroll;
  }
  #cs-navigation .cs-li {
    text-align: center;
    list-style: none;
    width: 100%;
    margin-right: 0;
    opacity: 0;
    /* transition from these values */
    transform: translateY(-4.375rem);
    transition: transform 0.6s, opacity 0.9s;
  }
  #cs-navigation .cs-li:nth-of-type(1) {
    transition-delay: 0.05s;
  }
  #cs-navigation .cs-li:nth-of-type(2) {
    transition-delay: 0.1s;
  }
  #cs-navigation .cs-li:nth-of-type(3) {
    transition-delay: 0.15s;
  }
  #cs-navigation .cs-li:nth-of-type(4) {
    transition-delay: 0.2s;
  }
  #cs-navigation .cs-li:nth-of-type(5) {
    transition-delay: 0.25s;
  }
  #cs-navigation .cs-li:nth-of-type(6) {
    transition-delay: 0.3s;
  }
  #cs-navigation .cs-li:nth-of-type(7) {
    transition-delay: 0.35s;
  }
  #cs-navigation .cs-li:nth-of-type(8) {
    transition-delay: 0.4s;
  }
  #cs-navigation .cs-li:nth-of-type(9) {
    transition-delay: 0.45s;
  }
  #cs-navigation .cs-li:nth-of-type(10) {
    transition-delay: 0.5s;
  }
  #cs-navigation .cs-li:nth-of-type(11) {
    transition-delay: 0.55s;
  }
  #cs-navigation .cs-li:nth-of-type(12) {
    transition-delay: 0.6s;
  }
  #cs-navigation .cs-li:nth-of-type(13) {
    transition-delay: 0.65s;
  }
  #cs-navigation .cs-li-link {
    /* 16px - 24px */
    font-size: clamp(1rem, 2.5vw, 1.5rem);
    line-height: 1.2em;
    text-decoration: none;
    margin: 0;
    color: var(--headerColor);
    display: inline-block;
    position: relative;
  }
  #cs-navigation .cs-li-link.cs-active {
    color: var(--primary);
  }
  #cs-navigation .cs-li-link:hover {
    color: var(--primary);
  }
  #cs-navigation .cs-button-solid {
    display: none;
  }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #cs-navigation .cs-top-link:nth-of-type(2) {
    display: flex;
  }
}
/* Dark Mode */
@media only screen and (max-width: 1023px) {
  body.dark-mode #cs-navigation {
    background-color: var(--dark);
  }
  body.dark-mode #cs-navigation .cs-top-container {
    background-color: rgba(0, 0, 0, 0.2);
  }
  body.dark-mode #cs-navigation .cs-top-link,
  body.dark-mode #cs-navigation .cs-desc {
    color: var(--bodyTextColorWhite);
    opacity: 0.8;
  }
  body.dark-mode #cs-navigation .cs-link-icon {
    filter: grayscale(1) brightness(1000%);
  }
  body.dark-mode #cs-navigation .cs-ul-wrapper {
    background-color: var(--medium);
  }
  body.dark-mode #cs-navigation .cs-li-link,
  body.dark-mode #cs-navigation .cs-header {
    color: var(--bodyTextColorWhite);
  }
}
/*-- -------------------------- -->
<---     Navigation Dropdown    -->
<--- -------------------------- -*/
/* Mobile - 1023px */
@media only screen and (max-width: 63.9375rem) {
  #cs-navigation .cs-dropdown {
    position: relative;
    color: var(--bodyTextColorWhite);
  }
  #cs-navigation .cs-dropdown .cs-li-link {
    position: relative;
    transition: opacity 0.3s;
  }
  #cs-navigation .cs-dropdown.cs-active .cs-drop-ul {
    visibility: visible;
    opacity: 1;
    transform: scale(1);
    margin: 0.75rem 0 0 0;
    height: auto;
    padding: 1.5rem;
  }
  #cs-navigation .cs-dropdown.cs-active .cs-drop-link {
    opacity: 1;
  }
  #cs-navigation .cs-dropdown-button {
    border: none;
    background-color: transparent;
    font-family: inherit;
    font-size: clamp(1rem, 2.5vw, 1.5rem);
    cursor: pointer;
    appearance: none;
  }
  #cs-navigation .cs-drop-icon {
    position: absolute;
    top: 50%;
    right: -1.25rem;
    transform: translateY(-50%);
    height: auto;
    width: 0.9375rem;
  }
  #cs-navigation .cs-drop-ul {
    visibility: hidden;
    opacity: 0;
    transform: scale(0);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-end;
    margin: 0;
    background-color: var(--primary);
    height: 0;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    padding: 0 1.5rem 0 1.5rem;
    transition: padding 0.3s, margin 0.3s, height 0.3s, opacity 0.3s, transform 0.3s, visibility 0.3s;
    gap: 0.75rem;
    transform-origin: top right;
  }
  #cs-navigation .cs-drop-li {
    list-style: none;
    text-align: inherit;
  }
  #cs-navigation .cs-li-link.cs-drop-link {
    /* 14px - 16px */
    font-size: clamp(0.875rem, 2vw, 1.25rem);
    color: #fff;
  }
}
/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
  #cs-navigation .cs-dropdown {
    position: relative;
  }
  #cs-navigation .cs-dropdown.cs-active, #cs-navigation .cs-dropdown:hover {
    cursor: pointer;
  }
  #cs-navigation .cs-dropdown.cs-active .cs-drop-ul, #cs-navigation .cs-dropdown:hover .cs-drop-ul {
    visibility: visible;
    opacity: 1;
    transform: scaleY(1);
  }
  #cs-navigation .cs-dropdown.cs-active .cs-drop-li, #cs-navigation .cs-dropdown:hover .cs-drop-li {
    opacity: 1;
    transform: translateY(0);
  }
  #cs-navigation .cs-dropdown-button {
    border: none;
    background-color: transparent;
    font-family: inherit;
    font-size: inherit;
    cursor: pointer;
    appearance: none;
  }
  #cs-navigation .cs-drop-icon {
    display: inline-block;
    height: auto;
    width: 0.9375rem;
  }
  #cs-navigation .cs-drop-ul {
    z-index: -100;
    /* if you have 8 or more links in your dropdown nav, uncomment the columns property to make the list into 2 even columns. Change it to 3 or 4 if you need extra columns. Then remove the transition delays on the cs-drop-li so they don't have weird scattered animations */
    position: absolute;
    top: 100%;
    visibility: hidden;
    opacity: 0;
    transform: scaleY(0);
    border-bottom: 5px solid var(--primary);
    margin: 0;
    background-color: #fff;
    min-width: 12.5rem;
    overflow: hidden;
    padding: 0;
    transition: transform 0.3s, visibility 0.3s, opacity 0.3s;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 10px 16px;
    transform-origin: top;
  }
  #cs-navigation .cs-drop-li {
    opacity: 0;
    transform: translateY(-0.625rem);
    display: block;
    height: auto;
    width: 100%;
    list-style: none;
    text-decoration: none;
    font-size: 1rem;
    color: var(--bodyTextColor);
    transition: opacity 0.6s, transform 0.6s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(1) {
    transition-delay: 0.05s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(2) {
    transition-delay: 0.1s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(3) {
    transition-delay: 0.15s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(4) {
    transition-delay: 0.2s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(5) {
    transition-delay: 0.25s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(6) {
    transition-delay: 0.3s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(7) {
    transition-delay: 0.35s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(8) {
    transition-delay: 0.4s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(9) {
    transition-delay: 0.45s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(10) {
    transition-delay: 0.5s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(11) {
    transition-delay: 0.55s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(12) {
    transition-delay: 0.6s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(13) {
    transition-delay: 0.65s;
  }
  #cs-navigation .cs-li-link.cs-drop-link {
    display: block;
    width: 100%;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    padding: 0.75rem;
    white-space: nowrap;
    text-decoration: none;
    font-size: 1rem;
    line-height: 1.5em;
    color: var(--bodyTextColor);
    transition: color 0.3s, background-color 0.3s;
  }
  #cs-navigation .cs-li-link.cs-drop-link:hover {
    background-color: #f7f7f7;
  }
  #cs-navigation .cs-li-link.cs-drop-link:focus-visible {
    outline: 2px solid currentColor;
    outline-offset: -4px;
  }
  #cs-navigation .cs-li-link.cs-drop-link:before {
    display: none;
  }
}
/* Dark Mode */
@media only screen and (min-width: 0rem) {
  body.dark-mode #cs-navigation .cs-drop-ul {
    background-color: var(--dark);
  }
  body.dark-mode #cs-navigation .cs-li-link.cs-drop-link:hover {
    background-color: var(--medium);
  }
  body.dark-mode #cs-navigation .cs-drop-icon {
    /* turns it white */
    filter: grayscale(1) brightness(1000%);
  }
}
/*-- -------------------------- -->
<---     Desktop Navigation     -->
<--- -------------------------- -*/
/* Small Desktop - 1024px */
@media only screen and (min-width: 64rem) {
  body.scroll #cs-navigation {
    /* 53px, the ssme height as the cs-top-container */
    transform: translateY(-3.3125rem);
  }
  #cs-navigation {
    width: 100%;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    padding: 0;
    background-color: #fff;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    position: fixed;
    z-index: 10000;
    transition: transform 0.3s;
  }
  #cs-navigation .cs-top-bar {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  #cs-navigation .cs-top-container {
    width: 100%;
    max-width: 80rem;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 3.125rem;
    position: relative;
    z-index: 1;
  }
  #cs-navigation .cs-top-container:before {
    /* grey background */
    content: "";
    width: 100vw;
    height: 100%;
    background: #f7f7f7;
    opacity: 1;
    display: block;
    position: absolute;
    top: 0;
    left: 50%;
    z-index: -1;
    transform: translateX(-50%);
  }
  #cs-navigation .cs-top-contact {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 1.5rem;
  }
  #cs-navigation .cs-top-link {
    font-size: 0.875rem;
    line-height: 1.5em;
    text-decoration: none;
    margin: 0;
    color: var(--bodyTextColor);
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 0.5rem;
    position: relative;
  }
  #cs-navigation .cs-top-link:hover {
    text-decoration: underline;
  }
  #cs-navigation .cs-link-icon {
    width: 1rem;
    height: auto;
    display: block;
  }
  #cs-navigation .cs-top-social {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
  }
  #cs-navigation .cs-social-link {
    text-decoration: none;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform 0.3s;
  }
  #cs-navigation .cs-social-link:hover {
    transform: scale(1.1);
  }
  #cs-navigation .cs-social-icon {
    width: 1.25rem;
    height: auto;
    display: block;
  }
  #cs-navigation .cs-container {
    width: 100%;
    max-width: 80rem;
    margin: auto;
    /* prevents padding from affectin gheight */
    box-sizing: border-box;
    padding: 0 1rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 1.5rem;
    position: relative;
  }
  #cs-navigation .cs-toggle {
    display: none;
  }
  #cs-navigation .cs-logo {
    /* 40px - 44px */
    height: clamp(2.5rem, 4vw, 2.75rem);
    /* margin-right auto pushes everything away from it to the right */
    margin: 0 auto 0 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
  }
  #cs-navigation .cs-logo img {
    width: auto;
    height: 100%;
    /* ensures the image never overflows the container. It stays contained within it's width and height and expands to fill it then stops once it reaches an edge */
    object-fit: contain;
  }
  #cs-navigation .cs-ul {
    width: 100%;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 3rem;
  }
  #cs-navigation .cs-li {
    list-style: none;
    padding: 1.9375rem 0;
    /* prevent flexbox from squishing it */
    flex: none;
  }
  #cs-navigation .cs-li-link {
    /* 14px - 16px */
    font-size: clamp(0.875rem, 1.3vw, 1rem);
    line-height: 1.5em;
    text-decoration: none;
    margin: 0;
    color: var(--headerColor);
    display: block;
    position: relative;
    transition: color 0.3s;
  }
  #cs-navigation .cs-li-link:hover {
    color: var(--primary);
  }
  #cs-navigation .cs-li-link.cs-active {
    font-weight: 700;
    color: var(--headerColor);
  }
  #cs-navigation .cs-button-solid {
    font-size: 1rem;
    font-weight: 700;
    /* 46px - 56px */
    line-height: clamp(2.875em, 5.5vw, 3.5em);
    text-align: center;
    text-decoration: none;
    margin: 0;
    /* prevents padding from adding to the width */
    box-sizing: border-box;
    padding: 0 2rem;
    color: #fff;
    background-color: var(--primary);
    display: inline-block;
    position: relative;
    z-index: 1;
    overflow: hidden;
    transition: color 0.3s;
  }
  #cs-navigation .cs-button-solid:before {
    content: "";
    width: 0%;
    height: 100%;
    background: #1a1a1a;
    opacity: 1;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    transition: width 0.3s;
  }
  #cs-navigation .cs-button-solid:hover:before {
    width: 100%;
  }
  #cs-navigation .cs-nav-button {
    line-height: 2.875rem;
    margin-left: 1.5rem;
  }
}
/* Dark Mode */
@media only screen and (min-width: 64rem) {
  body.dark-mode #cs-navigation {
    background-color: var(--dark);
  }
  body.dark-mode #cs-navigation .cs-top-container:before {
    background-color: rgba(0, 0, 0, 0.2);
  }
  body.dark-mode #cs-navigation .cs-li-link,
  body.dark-mode #cs-navigation .cs-top-link {
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #cs-navigation .cs-li-link:hover,
  body.dark-mode #cs-navigation .cs-top-link:hover {
    color: var(--primary);
  }
  body.dark-mode #cs-navigation .cs-top-link {
    opacity: 0.8;
  }
  body.dark-mode #cs-navigation .cs-link-icon {
    /* turns it white */
    filter: grayscale(1) brightness(1000%);
  }
}
/*-- -------------------------- -->
<---          Banner            -->
<--- -------------------------- -*/
/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #banner-843 {
    /* 175px - 260px top */
    padding: clamp(10.9375rem, 20vw, 16.25rem) 1rem clamp(6.25rem, 14vw, 12vw);
    position: relative;
    z-index: 1;
    /* clips svg wave from causing overflow issues */
    overflow: hidden;
  }
  #banner-843 .cs-container {
    text-align: center;
    max-width: 80rem;
    width: 100%;
    margin: auto;
    display: flex;
    flex-direction: column;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 1rem;
  }
  #banner-843 .cs-int-title {
    /* 39px - 61px */
    font-size: clamp(2.4375rem, 6.4vw, 3.8125rem);
    line-height: 1.2em;
    font-weight: 900;
    text-align: inherit;
    margin: 0;
    color: var(--bodyTextColorWhite);
    position: relative;
  }
  #banner-843 .cs-background {
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
  }
  #banner-843 .cs-background:before {
    /* background color overlay */
    content: "";
    width: 100%;
    height: 100%;
    background: #1a1a1a;
    opacity: 0.64;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
  }
  #banner-843 .cs-background img {
    width: 100%;
    height: 100%;
    /* Makes img tag act as a background image */
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
  }
  #banner-843 .cs-wave {
    /* we're stretching the svg wider than the viewport so it's taller and has more of a presence */
    width: 48rem;
    height: auto;
    display: block;
    position: absolute;
    bottom: -1px;
    left: 50%;
    z-index: -1;
    transform: translateX(-50%);
  }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #banner-843 .cs-wave {
    width: 100%;
    left: 0;
    transform: none;
  }
}
/* Dark Mode */
@media only screen and (min-width: 0rem) {
  body.dark-mode #banner-843 .cs-wave path {
    fill: var(--dark);
  }
}
/*-- -------------------------- -->
<---            CTA             -->
<--- -------------------------- -*/
/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #cta-51 {
    padding: var(--sectionPadding);
    position: relative;
  }
  #cta-51 .cs-container {
    width: 100%;
    max-width: 80rem;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    /* 48px - 64px */
    gap: clamp(3rem, 6vw, 4rem);
  }
  #cta-51 .cs-content {
    /* set text align to left if content needs to be left aligned */
    text-align: center;
    width: 100%;
    display: flex;
    flex-direction: column;
    /* centers content horizontally, set to flex-start to left align */
    align-items: center;
  }
  #cta-51 .cs-title {
    color: var(--bodyTextColorWhite);
  }
  #cta-51 .cs-text {
    margin-bottom: 1rem;
    color: var(--bodyTextColorWhite);
    opacity: 0.8;
  }
  #cta-51 .cs-text:last-of-type {
    margin-bottom: 2rem;
  }
  #cta-51 .cs-button-solid {
    font-size: 1rem;
    /* 46px - 56px */
    line-height: clamp(2.875rem, 5.5vw, 3.5rem);
    text-decoration: none;
    font-weight: 700;
    text-align: center;
    margin: 0;
    color: #fff;
    min-width: 9.375rem;
    padding: 0 1.5rem;
    background-color: var(--primary);
    border-radius: 0.25rem;
    display: inline-block;
    position: relative;
    z-index: 1;
    /* prevents padding from adding to the width */
    box-sizing: border-box;
  }
  #cta-51 .cs-button-solid:before {
    content: "";
    position: absolute;
    height: 100%;
    width: 0%;
    background: #000;
    opacity: 1;
    top: 0;
    left: 0;
    z-index: -1;
    border-radius: 0.25rem;
    transition: width 0.3s;
  }
  #cta-51 .cs-button-solid:hover:before {
    width: 100%;
  }
  #cta-51 .cs-picture {
    height: 100%;
    width: 100%;
    display: block;
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    z-index: -1;
  }
  #cta-51 .cs-picture:before {
    /* black color overlay */
    content: "";
    position: absolute;
    display: block;
    height: 100%;
    width: 100%;
    background: #000;
    opacity: 0.8;
    top: 0;
    left: 0;
    z-index: 1;
  }
  #cta-51 .cs-picture img {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    object-fit: cover;
  }
}
/*-- -------------------------- -->
<---          Footer            -->
<--- -------------------------- -*/
/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #cs-footer-1339 {
    padding: var(--sectionPadding);
    /* 30px - 50px */
    padding-bottom: clamp(1.875rem, 5vw, 3.125rem);
    background-color: #1a1a1a;
    position: relative;
  }
  #cs-footer-1339 .cs-container {
    width: 100%;
    max-width: 80rem;
    margin: auto;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    column-gap: 3rem;
    row-gap: 2.5rem;
  }
  #cs-footer-1339 .cs-logo-group {
    /* takes up all the space, lets the other ul's wrap below it */
    width: 100%;
    position: relative;
  }
  #cs-footer-1339 .cs-title {
    margin: 0 0 2rem 0;
    max-width: 23ch;
    color: var(--bodyTextColorWhite);
  }
  #cs-footer-1339 .cs-social {
    display: inline-flex;
    justify-content: flex-start;
    gap: 0.5rem;
  }
  #cs-footer-1339 .cs-social-link {
    width: 2rem;
    height: 2rem;
    background-color: #484848;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 1;
    transition: transform 0.3s, background-color 0.3s;
  }
  #cs-footer-1339 .cs-social-link:hover {
    background-color: var(--primaryLight);
    transform: translateY(-0.1875rem);
  }
  #cs-footer-1339 .cs-social-link:hover .cs-social-img {
    filter: grayscale(1) brightness(100%);
  }
  #cs-footer-1339 .cs-social-img {
    height: 0.75rem;
    filter: grayscale(1) brightness(630%);
    width: auto;
    display: block;
  }
  #cs-footer-1339 .cs-header {
    font-size: 1.25rem;
    line-height: 1.5em;
    font-weight: 700;
    margin: 0 0 1.5rem 0;
    color: #fff;
    display: block;
    position: relative;
  }
  #cs-footer-1339 .cs-ul {
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
  #cs-footer-1339 .cs-li {
    list-style: none;
  }
  #cs-footer-1339 .cs-link {
    /* 14px - 16px */
    font-size: clamp(0.875rem, 1.5vw, 1rem);
    text-decoration: none;
    line-height: 1.5em;
    text-align: left;
    color: var(--bodyTextColorWhite);
    display: inline-flex;
    align-items: flex-start;
    gap: 0.5rem;
    transition: color 0.3s;
  }
  #cs-footer-1339 .cs-link:hover {
    color: var(--primaryLight);
  }
  #cs-footer-1339 .cs-icon {
    width: 1.25rem;
    height: auto;
    margin-right: 0.125rem;
    display: block;
  }
  #cs-footer-1339 .cs-bottom {
    text-align: center;
    width: 100%;
    max-width: 80rem;
    /* 48px - 64px */
    margin: clamp(1.75rem, 7vw, 4rem) auto 0;
    /* 28px - 40px */
    padding-top: clamp(1.75rem, 3vw, 2.5rem);
    border-top: 1px solid #484848;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.75rem;
  }
  #cs-footer-1339 .cs-credit,
  #cs-footer-1339 .cs-bottom-link,
  #cs-footer-1339 .cs-credit-link {
    /* 14px - 16px */
    font-size: clamp(0.875rem, 1.5vw, 1rem);
    line-height: 1.5em;
    text-decoration: none;
    width: auto;
    margin: 0;
    color: #bababa;
    display: inline-block;
    position: relative;
    transition: color 0.3s;
  }
  #cs-footer-1339 .cs-credit {
    width: 100%;
  }
  #cs-footer-1339 .cs-credit-link {
    /* 14px - 16px */
    font-size: clamp(0.875rem, 1.5vw, 1rem);
    line-height: 1.5em;
    text-decoration: none;
    width: auto;
    margin: 0;
    color: #bababa;
    display: inline-block;
    position: relative;
  }
  #cs-footer-1339 .cs-credit-link:hover {
    color: var(--primary);
  }
  #cs-footer-1339 .cs-bottom-links {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  #cs-footer-1339 .cs-bottom-link {
    display: flex;
    align-items: center;
  }
  #cs-footer-1339 .cs-bottom-link:hover {
    color: var(--primary);
  }
  #cs-footer-1339 .cs-bottom-link:last-of-type:before {
    /* seperator */
    content: "";
    width: 1px;
    height: 0.875rem;
    margin: 0 0.75rem;
    background: currentColor;
    opacity: 1;
    display: block;
  }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #cs-footer-1339 .cs-container {
    max-width: 80rem;
    /* 48px - 120px */
    column-gap: clamp(3rem, 8vw, 7.5rem);
    flex-wrap: nowrap;
  }
  #cs-footer-1339 .cs-logo-group {
    width: 45%;
    max-width: 38.875rem;
    /* pushes the lists away from it and to the right */
    margin-right: auto;
  }
  #cs-footer-1339 .cs-bottom {
    justify-content: space-between;
    flex-wrap: nowrap;
  }
  #cs-footer-1339 .cs-credit {
    text-align: left;
  }
  #cs-footer-1339 .cs-bottom-links {
    justify-content: flex-end;
  }
}
/* Dark Mode */
@media only screen and (min-width: 48rem) {
  body.dark-mode #cs-footer-1339 {
    background-color: rgba(0, 0, 0, 0.6);
  }
}

/*# sourceMappingURL=root.css.map */
