/*-- -------------------------- -->
<---        Content Page        -->
<--- -------------------------- -*/
/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #content-page-713 {
    padding: var(--sectionPadding);
    background-color: #fff;
    /* clips the wave background from causing overflow issues when it goes off screen */
    overflow: hidden;
    position: relative;
    z-index: 1;
  }
  #content-page-713 .cs-container {
    width: 100%;
    max-width: 80rem;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    /* 48px - 64px */
    gap: clamp(3rem, 6vw, 4rem);
    position: relative;
  }
  #content-page-713 .cs-content {
    /* set text align to left if content needs to be left aligned */
    text-align: left;
    width: 100%;
    max-width: 46.125rem;
    display: flex;
    flex-direction: column;
    /* centers content horizontally, set to flex-start to left align */
    align-items: flex-start;
  }
  #content-page-713 .cs-content img {
    width: 100%;
    height: auto;
    margin: 1rem 0;
    display: block;
  }
  #content-page-713 .cs-title {
    font-size: var(--headerFontSize);
    font-weight: 900;
    line-height: 1.2em;
    text-align: inherit;
    width: 100%;
    max-width: 100%;
    margin: 0 0 1rem 0;
    color: var(--headerColor);
    position: relative;
  }
  #content-page-713 h2,
  #content-page-713 h3,
  #content-page-713 h4,
  #content-page-713 h5,
  #content-page-713 h6 {
    font-weight: 700;
    text-align: inherit;
    margin: 0 0 1rem 0;
    color: var(--headerColor);
  }
  #content-page-713 h2 {
    font-size: 2rem;
    margin-top: 2rem;
  }
  #content-page-713 h3 {
    font-size: 1.5rem;
    color: var(--primary);
  }
  #content-page-713 h4,
  #content-page-713 h5,
  #content-page-713 h6 {
    font-size: 1.25rem;
  }
  #content-page-713 .cs-button-solid {
    margin-bottom: 2rem;
  }
  #content-page-713 .cs-color {
    color: var(--primary);
  }
  #content-page-713 p {
    font-size: var(--bodyFontSize);
    line-height: 1.5em;
    text-align: inherit;
    width: 100%;
    margin: 0 0 1rem 0;
    color: var(--bodyTextColor);
  }
  #content-page-713 p:last-of-type {
    margin-bottom: 2rem;
  }
  #content-page-713 p a {
    font-size: inherit;
    line-height: inherit;
    text-decoration: underline;
    color: var(--primary);
  }
  #content-page-713 ol {
    padding-left: 1.5rem;
    margin: 0 0 2rem 0;
    color: var(--bodyTextColor);
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  #content-page-713 ul {
    padding-left: 1.5rem;
    margin: 0 0 2rem 0;
    color: var(--bodyTextColor);
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  #content-page-713 ul li {
    list-style: none;
    color: inherit;
    position: relative;
  }
  #content-page-713 ul li:before {
    /* custom list bullet */
    content: "";
    width: 3px;
    height: 3px;
    background: currentColor;
    opacity: 1;
    border-radius: 50%;
    position: absolute;
    display: block;
    top: 0.625rem;
    left: -0.75rem;
  }
  #content-page-713 .cs-image-group {
    /* scales the whole section down and ties the font size to the vw and stops at 70% of the vale of 1em, changes at desktop */
    font-size: min(1vw, 1em);
    /* everything inside this box is in ems so we can scale it all down proportionally with a font size */
    width: 33.875em;
    height: 48.3125em;
    display: none;
    /* prevents flexbox from squishing it */
    flex: none;
    position: relative;
    /* flips it horizontally */
    transform: scaleX(-1);
  }
  #content-page-713 .cs-picture {
    width: 33.875em;
    height: 40.4375em;
    border-radius: 17.8125em;
    border: 0.75em solid #ffffff;
    background-color: #f7f7f7;
    /* prevents border from affecting height and width */
    box-sizing: border-box;
    /* clips img tag corners */
    overflow: hidden;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
  }
  #content-page-713 .cs-picture img {
    width: 100%;
    height: 100%;
    /* makes it act like a background image */
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    /* flips the image to its original orientation */
    transform: scaleX(-1);
  }
  #content-page-713 .cs-flower {
    width: 22.625em;
    height: auto;
    display: block;
    position: absolute;
    bottom: -2.375em;
    right: -3em;
    z-index: -1;
    transform: rotate(142deg);
  }
  #content-page-713 .cs-sense {
    width: 5em;
    height: auto;
    position: absolute;
    top: -0.25em;
    left: 0.625em;
    transform: rotate(90deg);
  }
  #content-page-713 .cs-background {
    display: none;
  }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #content-page-713 .cs-container {
    flex-direction: row;
    align-items: flex-start;
  }
  #content-page-713 .cs-image-group {
    display: block;
  }
  #content-page-713 .cs-background {
    width: 20%;
    height: 100%;
    background-color: #f7f7f7;
    display: block;
    position: absolute;
    right: 0;
    top: 0;
    z-index: -1;
  }
  #content-page-713 .cs-background img {
    width: 100%;
    height: 100%;
    opacity: 0.2;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
  }
}
/* Large Desktop 1300px */
@media only screen and (min-width: 81.25rem) {
  #content-page-713 .cs-image-group {
    /* position absolute so we can have it overflow the container as seen in the design. */
    font-size: inherit;
    position: absolute;
    top: 0rem;
    right: -6.25rem;
  }
  #content-page-713 .cs-background {
    width: 50%;
    /* with the left edge always positioned at the center line, we push left of the center line by 335px.  This ensures that this element will stay exactly in this position no matter how wide the screen gets */
    margin-left: 20.9375rem;
    right: auto;
    /* sets the left edge of the element to be at the center line */
    left: 50%;
  }
}
/* Dark Mode */
@media only screen and (min-width: 0rem) {
  body.dark-mode #content-page-713 {
    background-color: var(--dark);
  }
  body.dark-mode #content-page-713 .cs-title,
  body.dark-mode #content-page-713 .cs-text,
  body.dark-mode #content-page-713 h2,
  body.dark-mode #content-page-713 h3,
  body.dark-mode #content-page-713 h4,
  body.dark-mode #content-page-713 h5,
  body.dark-mode #content-page-713 h6,
  body.dark-mode #content-page-713 li,
  body.dark-mode #content-page-713 p {
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #content-page-713 .cs-color,
  body.dark-mode #content-page-713 a {
    color: var(--primaryLight);
  }
  body.dark-mode #content-page-713 p,
  body.dark-mode #content-page-713 li {
    color: #ebebeb;
  }
  body.dark-mode #content-page-713 .cs-picture {
    border-color: var(--dark);
    background-color: var(--dark);
  }
  body.dark-mode #content-page-713 .cs-flower {
    filter: brightness(50%);
  }
  body.dark-mode #content-page-713 .cs-sense {
    filter: brightness(150%);
  }
  body.dark-mode #content-page-713 .cs-background {
    background-color: var(--medium);
    filter: brightness(70%);
  }
  body.dark-mode #content-page-713 .cs-background img {
    opacity: 0.1;
  }
}
/*-- -------------------------- -->
<---          Services          -->
<--- -------------------------- -*/
/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #services-367 {
    padding: var(--sectionPadding);
  }
  @keyframes floatAnimation2 {
    0% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-4px);
    }
    70% {
      transform: translateY(4px);
    }
    100% {
      transform: translateY(0);
    }
  }
  @keyframes InAndOut {
    0% {
      transform: translateY(3.75rem) scale(1);
      opacity: 1;
    }
    30% {
      transform: translateY(1.875rem) scale(0.8);
      opacity: 1;
    }
    60% {
      transform: translateY(0.625rem) scale(0.5);
      opacity: 0.2;
    }
    100% {
      transform: translateY(0rem) scale(0.2);
      opacity: 0;
    }
  }
  @keyframes InAndOut2 {
    0% {
      transform: translateY(4.375rem) scale(1);
      opacity: 1;
    }
    30% {
      transform: translateY(2.5rem) scale(0.8);
      opacity: 1;
    }
    60% {
      transform: translateY(1.25rem) scale(0.6);
      opacity: 1;
    }
    100% {
      transform: translateY(0rem) scale(0.4);
      opacity: 0;
    }
  }
  #services-367 .cs-container {
    width: 100%;
    /* changes to 1440px at desktop */
    max-width: 59rem;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    /* 48px - 64px */
    gap: clamp(3rem, 6vw, 4rem);
  }
  #services-367 .cs-content {
    /* set text align to left if content needs to be left aligned */
    text-align: center;
    width: 100%;
    display: flex;
    flex-direction: column;
    /* centers content horizontally, set to flex-start to left align */
    align-items: center;
  }
  #services-367 .cs-card-group {
    width: 100%;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    flex-direction: column;
    /* 16px - 20px */
    gap: clamp(1rem, 2vw, 1.25rem);
  }
  #services-367 .cs-item {
    list-style: none;
    width: 100%;
    max-width: 31.25rem;
    /* 20px - 40px top & bottom */
    /* 16px - 32px left & right */
    padding: clamp(1.25rem, 3.5vw, 2.5rem) clamp(1rem, 2.5vw, 2rem);
    border-radius: 1rem;
    border: 1px solid #e8e8e8;
    box-sizing: border-box;
    transition: background-color 0.3s;
  }
  #services-367 .cs-item:hover {
    background-color: var(--primary);
  }
  #services-367 .cs-item:hover .cs-icon-wrapper:before, #services-367 .cs-item:hover .cs-icon-wrapper:after,
  #services-367 .cs-item:hover .cs-bubble:before,
  #services-367 .cs-item:hover .cs-bubble:after {
    background-color: #449ffc;
  }
  #services-367 .cs-item:hover .cs-icon {
    /* makes it white on hover */
    filter: grayscale(1) brightness(1000%);
  }
  #services-367 .cs-item:hover .cs-h3 {
    color: #fff;
  }
  #services-367 .cs-item:hover .cs-item-text {
    color: #fff;
  }
  #services-367 .cs-link {
    text-decoration: none;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }
  #services-367 .cs-icon-wrapper {
    width: 6.125rem;
    height: 7.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    position: relative;
  }
  #services-367 .cs-icon-wrapper:before {
    /* Small Bubble */
    content: "";
    width: 1.6875rem;
    height: 1.6875rem;
    border-radius: 50%;
    background: #e5f2ff;
    opacity: 1;
    position: absolute;
    display: block;
    top: 0;
    left: 0.1875rem;
    animation-name: InAndOut;
    animation-duration: 1.6s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    transition: background-color 0.3s;
  }
  #services-367 .cs-icon-wrapper:after {
    /* Small Bubble */
    content: "";
    width: 1.6875rem;
    height: 1.6875rem;
    border-radius: 50%;
    background: #e5f2ff;
    opacity: 1;
    position: absolute;
    display: block;
    top: 0.6875rem;
    right: 0.3125rem;
    animation-name: InAndOut2;
    animation-duration: 2s;
    animation-delay: 0.3s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    transition: background-color 0.3s;
  }
  #services-367 .cs-bubble {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
  #services-367 .cs-bubble:before {
    /* Big Bubble */
    content: "";
    width: 3.9375rem;
    height: 3.9375rem;
    border-radius: 50%;
    background: #e5f2ff;
    opacity: 1;
    position: absolute;
    display: block;
    bottom: 0.25rem;
    left: 0rem;
    animation-name: floatAnimation2;
    animation-duration: 12s;
    animation-timing-function: ease-in-out;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
    transition: background-color 0.3s;
  }
  #services-367 .cs-bubble:after {
    /* Big Bubble */
    content: "";
    width: 3.125rem;
    height: 3.125rem;
    border-radius: 50%;
    background: #e5f2ff;
    opacity: 1;
    position: absolute;
    display: block;
    bottom: -0.25rem;
    right: 0rem;
    animation-name: floatAnimation2;
    animation-duration: 10s;
    animation-timing-function: ease-in-out;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
    transition: background-color 0.3s;
  }
  #services-367 .cs-icon {
    width: 4.5rem;
    height: auto;
    position: relative;
    z-index: 10;
  }
  #services-367 .cs-h3 {
    font-size: 1.25rem;
    font-weight: 900;
    text-align: center;
    line-height: 1.2em;
    margin: 0;
    margin-bottom: 0.75rem;
    color: var(--headerColor);
    transition: color 0.3s;
  }
  #services-367 .cs-item-text {
    /* 14px - 16px */
    font-size: clamp(0.875rem, 1.5vw, 1rem);
    text-align: center;
    line-height: 1.5em;
    margin: 0;
    color: var(--bodyTextColor);
    transition: color 0.3s;
  }
  #services-367 .cs-button-solid {
    font-size: 1rem;
    /* 46px - 56px */
    line-height: clamp(2.875rem, 5.5vw, 3.5rem);
    text-decoration: none;
    font-weight: 700;
    text-align: center;
    margin: 0;
    color: #fff;
    min-width: 9.375rem;
    padding: 0 1.5rem;
    background-color: var(--primary);
    border-radius: 0.25rem;
    display: inline-block;
    position: relative;
    z-index: 1;
    /* prevents padding from adding to the width */
    box-sizing: border-box;
  }
  #services-367 .cs-button-solid:before {
    content: "";
    position: absolute;
    height: 100%;
    width: 0%;
    background: #000;
    opacity: 1;
    top: 0;
    left: 0;
    z-index: -1;
    border-radius: 0.25rem;
    transition: width 0.3s;
  }
  #services-367 .cs-button-solid:hover:before {
    width: 100%;
  }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #services-367 .cs-card-group {
    flex-direction: row;
    justify-content: center;
  }
  #services-367 .cs-item {
    width: 48%;
  }
}
/* Desktop - 1200px */
@media only screen and (min-width: 75rem) {
  #services-367 .cs-container {
    max-width: 90rem;
  }
  #services-367 .cs-item {
    /* we do this so it's stackable. You can add any number of reviews you want and they will stack and center in the middle. We dont use grid because if you have an odd number of cards, they don't stay centered, they align with their grid lines. This way its more FLEX-ible*/
    width: clamp(23.47%, 23vw, 23.955%);
  }
}
/* Dark Mode */
@media only screen and (min-width: 0rem) {
  body.dark-mode #services-367 .cs-topper {
    color: var(--primaryLight);
  }
  body.dark-mode #services-367 .cs-title,
  body.dark-mode #services-367 .cs-text,
  body.dark-mode #services-367 .cs-h3,
  body.dark-mode #services-367 .cs-item-text {
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #services-367 .cs-icon-wrapper:before, body.dark-mode #services-367 .cs-icon-wrapper:after,
  body.dark-mode #services-367 .cs-bubble:before,
  body.dark-mode #services-367 .cs-bubble:after {
    background-color: var(--accent);
  }
  body.dark-mode #services-367 .cs-icon {
    /* turns the icon white */
    filter: grayscale(1) brightness(1000%);
  }
}

/*# sourceMappingURL=interior.css.map */
