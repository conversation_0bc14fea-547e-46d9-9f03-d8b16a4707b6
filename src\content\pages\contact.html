---
title: "Contact | TopTaxHQ Accounting"
description: "Get in touch with TopTaxHQ Accounting for all your accounting needs in the Denver Colorado area."
preloadImg: "/assets/images/banner-sm.webp"
permalink: "contact/"
eleventyNavigation:
    key: Contact Us
    order: 600
    hideOnDesktop: true
---

{% extends "layouts/base.html" %}

{% block head %}
    <link rel="stylesheet" href="/assets/css/contact.css" />
{% endblock %}

{% block body %}
    <!-- ============================================ -->
    <!--                   Banner                     -->
    <!-- ============================================ -->

    <div id="banner-843">
        <div class="cs-container">
            <h1 class="cs-int-title">Contact Us</h1>
        </div>
        <!--Background Image-->
        <picture class="cs-background" aria-hidden="true">
            <!--Mobile Image-->
            <source
                media="(max-width: 600px)"
                srcset="
                    /assets/images/banner-sm.webp
                "
            />
            <!--Tablet and above Image-->
            <source
                media="(min-width: 601px)"
                srcset="
                    /assets/images/banner.webp
                "
            />
            <img
                loading="lazy"
                decoding="async"
                src="/assets/images/banner.webp"
                alt="accounting banner"
                width="1280"
                height="568"
            />
        </picture>
        <!--Change the svg path fill color to whatever color the section below is so you can match it and create the illusion it is all one piece-->
        <svg
            class="cs-wave"
            width="1920"
            height="179"
            viewBox="0 0 1920 179"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M1920 179V91.3463C1835.33 91.3463 1715.47 76.615 1549.2 32.9521C1299.48 -32.3214 1132.77 12.1006 947.32 61.5167C810.762 97.9044 664.042 137 466.533 137C331.607 137 256.468 123.447 188.082 111.113C130.974 100.812 78.5746 91.3609 0 91.3609V179H1920Z"
                fill="white"
            />
        </svg>
    </div>

    <!-- ============================================ -->
    <!--                Contact Form                  -->
    <!-- ============================================ -->

    <section id="cs-contact">
        <div class="cs-container">
            <form id="cs-form" name="Contact Form" method="post" netlify>
                <div class="cs-content">
                    <span class="cs-topper">Contact</span>
                    <h2 class="cs-title">Get in Touch</h2>
                    <p class="cs-text">
                        Feel free to reach out to us and we will get back to you
                        as soon as possible.
                    </p>
                </div>
                <label>
                    Name
                    <input
                        required
                        type="text"
                        id="name"
                        name="name"
                        placeholder="Name"
                    />
                </label>
                <label>
                    Email
                    <input
                        required
                        type="text"
                        id="email"
                        name="email"
                        placeholder="Email"
                    />
                </label>
                <label>
                    Phone
                    <input
                        required
                        type="text"
                        id="phone"
                        name="phone"
                        placeholder="Phone"
                    />
                </label>
                <label>
                    How Did You Find Us
                    <input
                        type="text"
                        id="find"
                        name="find-us"
                        placeholder="How did you find us?"
                    />
                </label>
                <label class="cs-label-message">
                    Message
                    <textarea
                        required
                        name="Message"
                        id="message"
                        placeholder="Write message..."
                    ></textarea>
                </label>
                <button class="cs-button-solid" type="submit">
                    Submit Message
                </button>
            </form>
            <div class="cs-right-section">
                <!--Email-->
                <span class="cs-header">Email</span>
                <a class="cs-link" href="mailto:{{ client.email }}"
                    >{{ client.email }}</a
                >
                <!--Address-->
                <span class="cs-header">Address</span>
                <a class="cs-link" href="{{ client.address.mapLink }}">
                    <span class="cs-block">
                        {{ client.address.city }}
                        {{ client.address.state }}
                        {{ client.address.zip }}
                    </span>
                </a>

                <!-- Background Image-->
                <picture class="cs-bg-picture">
                    <img
                        aria-hidden="true"
                        decoding="async"
                        src="https://images.pexels.com/photos/4386375/pexels-photo-4386375.jpeg?auto=compress&cs=tinysrgb&w=1600"
                        alt="tax time"
                        loading="lazy"
                        width="2500"
                        height="1667"
                    />
                </picture>
            </div>
        </div>
    </section>
    
{% endblock %}