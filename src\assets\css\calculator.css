/*-- -------------------------- -->
<---      Calculator Section    -->
<--- -------------------------- -*/
/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #calculator-section {
    padding: var(--sectionPadding);
    background-color: #fff;
    position: relative;
  }
  #calculator-section .cs-container {
    width: 100%;
    max-width: 80rem;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: clamp(3rem, 6vw, 4rem);
  }
  #calculator-section .cs-tab-nav {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 2rem;
  }
  #calculator-section .cs-tab-button {
    /* 14px - 16px */
    font-size: clamp(0.875rem, 1.5vw, 1rem);
    line-height: 1.2em;
    text-decoration: none;
    font-weight: 700;
    text-align: center;
    margin: 0;
    color: var(--bodyTextColor);
    min-width: 9.375rem;
    padding: 0.75rem 1.5rem;
    background-color: transparent;
    border: 2px solid var(--primary);
    border-radius: 0.5rem;
    display: inline-block;
    position: relative;
    z-index: 1;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    transition: color 0.3s, background-color 0.3s, border-color 0.3s;
    cursor: pointer;
  }
  #calculator-section .cs-tab-button:before {
    content: "";
    position: absolute;
    height: 100%;
    width: 0%;
    background: var(--primary);
    opacity: 1;
    top: 0;
    left: 0;
    z-index: -1;
    border-radius: 0.375rem;
    transition: width 0.3s;
  }
  #calculator-section .cs-tab-button:hover {
    color: #fff;
  }
  #calculator-section .cs-tab-button:hover:before {
    width: 100%;
  }
  #calculator-section .cs-tab-button.active {
    color: #fff;
    background-color: var(--primary);
    border-color: var(--primary);
  }
  #calculator-section .cs-tab-button.active:before {
    width: 100%;
  }
  #calculator-section .cs-calculator-container {
    width: 100%;
    position: relative;
  }
  #calculator-section .cs-calculator {
    width: 100%;
    background-color: #f7f7f7;
    border-radius: 1rem;
    padding: clamp(2rem, 5vw, 3rem);
    display: none;
  }
  #calculator-section .cs-calculator.active {
    display: block;
  }
  #calculator-section .cs-calculator-title {
    /* 31px - 39px */
    font-size: clamp(1.9375rem, 3.9vw, 2.4375rem);
    font-weight: 900;
    line-height: 1.2em;
    text-align: center;
    max-width: 43.75rem;
    margin: 0 auto 1rem;
    color: var(--headerColor);
    position: relative;
  }
  #calculator-section .cs-calculator-description {
    font-size: 1rem;
    line-height: 1.5em;
    text-align: center;
    width: 100%;
    max-width: 39.375rem;
    margin: 0 auto 2rem;
    color: var(--bodyTextColor);
  }
  #calculator-section .cs-calculator-form {
    width: 100%;
    max-width: 39.375rem;
    margin: 0 auto 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  #calculator-section .cs-input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  #calculator-section .cs-input-group label {
    font-size: 1rem;
    font-weight: 700;
    line-height: 1.5em;
    color: var(--headerColor);
    display: block;
  }
  #calculator-section .cs-input-group input,
  #calculator-section .cs-input-group select {
    font-size: 1rem;
    width: 100%;
    height: 3.5rem;
    padding: 0 1.5rem;
    background-color: #fff;
    color: var(--headerColor);
    border: 1px solid #dadada;
    border-radius: 0.5rem;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    transition: border-color 0.3s;
  }
  #calculator-section .cs-input-group input:focus,
  #calculator-section .cs-input-group select:focus {
    outline: none;
    border-color: var(--primary);
  }
  #calculator-section .cs-input-group input::placeholder,
  #calculator-section .cs-input-group select::placeholder {
    color: #b4b2c7;
    opacity: 1;
  }
  #calculator-section .cs-input-group input[readonly],
  #calculator-section .cs-input-group select[readonly] {
    background-color: #f5f5f5;
    color: var(--bodyTextColor);
  }
  #calculator-section .cs-calculate-btn {
    font-size: 1rem;
    /* 46px - 56px */
    line-height: clamp(2.875rem, 5.5vw, 3.5rem);
    text-decoration: none;
    font-weight: 700;
    text-align: center;
    margin: 1rem auto 0;
    color: #fff;
    min-width: 12.5rem;
    padding: 0 1.5rem;
    background-color: var(--primary);
    border-radius: 0.5rem;
    border: none;
    display: inline-block;
    position: relative;
    z-index: 1;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    transition: color 0.3s, background-color 0.3s;
    cursor: pointer;
  }
  #calculator-section .cs-calculate-btn:before {
    content: "";
    position: absolute;
    height: 100%;
    width: 0%;
    background: #000;
    opacity: 1;
    top: 0;
    left: 0;
    z-index: -1;
    border-radius: 0.5rem;
    transition: width 0.3s;
  }
  #calculator-section .cs-calculate-btn:hover:before {
    width: 100%;
  }
  #calculator-section .cs-results {
    width: 100%;
    max-width: 39.375rem;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid #e8e8e8;
  }
  #calculator-section .cs-results h3 {
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1.2em;
    margin: 0 0 1rem;
    color: var(--headerColor);
    text-align: center;
  }
  #calculator-section .cs-result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
  }
  #calculator-section .cs-result-item:last-child {
    border-bottom: none;
  }
  #calculator-section .cs-result-item .cs-result-label {
    font-size: 1rem;
    font-weight: 600;
    color: var(--headerColor);
  }
  #calculator-section .cs-result-item .cs-result-value {
    font-size: 1rem;
    font-weight: 700;
    color: var(--primary);
    text-align: right;
  }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #calculator-section .cs-tab-nav {
    gap: 0.75rem;
  }
  #calculator-section .cs-tab-button {
    min-width: 11.25rem;
    padding: 1rem 2rem;
  }
  #calculator-section .cs-calculator-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }
  #calculator-section .cs-calculator-form .cs-calculate-btn {
    grid-column: 1/-1;
    justify-self: center;
  }
  #calculator-section .cs-results {
    padding: 2rem;
  }
  #calculator-section .cs-result-item {
    padding: 1rem 0;
  }
  #calculator-section .cs-result-item .cs-result-label,
  #calculator-section .cs-result-item .cs-result-value {
    font-size: 1.125rem;
  }
}
/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
  #calculator-section .cs-tab-nav {
    gap: 1rem;
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 0.5rem;
    /* Custom scrollbar */
  }
  #calculator-section .cs-tab-nav::-webkit-scrollbar {
    height: 4px;
  }
  #calculator-section .cs-tab-nav::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }
  #calculator-section .cs-tab-nav::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: 2px;
  }
  #calculator-section .cs-tab-button {
    min-width: 12.5rem;
    white-space: nowrap;
    flex-shrink: 0;
  }
  #calculator-section .cs-calculator {
    padding: clamp(3rem, 6vw, 4rem);
  }
  #calculator-section .cs-calculator-form {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
  }
  #calculator-section .cs-calculator-form .cs-calculate-btn {
    grid-column: 1/-1;
    max-width: 18.75rem;
  }
}
/* Dark Mode */
@media only screen and (min-width: 0rem) {
  body.dark-mode #calculator-section {
    background-color: var(--dark);
  }
  body.dark-mode #calculator-section .cs-calculator {
    background-color: var(--medium);
  }
  body.dark-mode #calculator-section .cs-calculator-title {
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #calculator-section .cs-calculator-description {
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #calculator-section .cs-input-group label {
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #calculator-section .cs-input-group input,
  body.dark-mode #calculator-section .cs-input-group select {
    background-color: var(--dark);
    color: var(--bodyTextColorWhite);
    border-color: var(--accent);
  }
  body.dark-mode #calculator-section .cs-input-group input:focus,
  body.dark-mode #calculator-section .cs-input-group select:focus {
    border-color: var(--primary);
  }
  body.dark-mode #calculator-section .cs-input-group input::placeholder,
  body.dark-mode #calculator-section .cs-input-group select::placeholder {
    color: #8e8e8e;
  }
  body.dark-mode #calculator-section .cs-input-group input[readonly],
  body.dark-mode #calculator-section .cs-input-group select[readonly] {
    background-color: var(--accent);
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #calculator-section .cs-results {
    background-color: var(--dark);
    border-color: var(--accent);
  }
  body.dark-mode #calculator-section .cs-results h3 {
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #calculator-section .cs-result-item {
    border-bottom-color: var(--accent);
  }
  body.dark-mode #calculator-section .cs-result-item .cs-result-label {
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #calculator-section .cs-result-item .cs-result-value {
    color: var(--primary);
  }
}

/*# sourceMappingURL=calculator.css.map */
