/*-- -------------------------- -->
<---        Content Page        -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #content-page-713 {
    padding: var(--sectionPadding);
    background-color: #fff;

    /* clips the wave background from causing overflow issues when it goes off screen */
    overflow: hidden;
    position: relative;
    z-index: 1;

    .cs-container {
      width: 100%;
      max-width: 80rem;
      margin: auto;
      display: flex;
      flex-direction: column;
      align-items: center;

      /* 48px - 64px */
      gap: clamp(3rem, 6vw, 4rem);
      position: relative;
    }

    .cs-content {
      /* set text align to left if content needs to be left aligned */
      text-align: left;
      width: 100%;
      max-width: 46.125rem;
      display: flex;
      flex-direction: column;

      /* centers content horizontally, set to flex-start to left align */
      align-items: flex-start;

      img {
        width: 100%;
        height: auto;
        margin: 1rem 0;
        display: block;
      }
    }

    .cs-title {
      font-size: var(--headerFontSize);
      font-weight: 900;
      line-height: 1.2em;
      text-align: inherit;
      width: 100%;
      max-width: 100%;
      margin: 0 0 1rem 0;
      color: var(--headerColor);
      position: relative;
    }

    h2,
    h3,
    h4,
    h5,
    h6 {
      font-weight: 700;
      text-align: inherit;
      margin: 0 0 1rem 0;
      color: var(--headerColor);
    }

    h2 {
      font-size: 2rem;
      margin-top: 2rem;
    }

    h3 {
      font-size: 1.5rem;
      color: var(--primary);
    }

    h4,
    h5,
    h6 {
      font-size: 1.25rem;
    }

    .cs-button-solid {
      margin-bottom: 2rem;
    }

    .cs-color {
      color: var(--primary);
    }

    p {
      font-size: var(--bodyFontSize);
      line-height: 1.5em;
      text-align: inherit;
      width: 100%;
      margin: 0 0 1rem 0;
      color: var(--bodyTextColor);

      &:last-of-type {
        margin-bottom: 2rem;
      }

      a {
        font-size: inherit;
        line-height: inherit;
        text-decoration: underline;
        color: var(--primary);
      }
    }

    ol {
      padding-left: 1.5rem;
      margin: 0 0 2rem 0;
      color: var(--bodyTextColor);
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    ul {
      padding-left: 1.5rem;
      margin: 0 0 2rem 0;
      color: var(--bodyTextColor);
      display: flex;
      flex-direction: column;
      gap: 1rem;

      li {
        list-style: none;
        color: inherit;
        position: relative;

        &:before {
          /* custom list bullet */
          content: "";
          width: 3px;
          height: 3px;
          background: currentColor;
          opacity: 1;
          border-radius: 50%;
          position: absolute;
          display: block;
          top: 0.625rem;
          left: -0.75rem;
        }
      }
    }

    .cs-image-group {
      /* scales the whole section down and ties the font size to the vw and stops at 70% of the vale of 1em, changes at desktop */
      font-size: min(1vw, 1em);

      /* everything inside this box is in ems so we can scale it all down proportionally with a font size */
      width: 33.875em;
      height: 48.3125em;
      display: none;

      /* prevents flexbox from squishing it */
      flex: none;
      position: relative;

      /* flips it horizontally */
      transform: scaleX(-1);
    }

    .cs-picture {
      width: 33.875em;
      height: 40.4375em;
      border-radius: 17.8125em;
      border: 0.75em solid #ffffff;
      background-color: #f7f7f7;

      /* prevents border from affecting height and width */
      box-sizing: border-box;

      /* clips img tag corners */
      overflow: hidden;
      display: block;
      position: absolute;
      top: 0;
      left: 0;

      img {
        width: 100%;
        height: 100%;

        /* makes it act like a background image */
        object-fit: cover;
        position: absolute;
        top: 0;
        left: 0;

        /* flips the image to its original orientation */
        transform: scaleX(-1);
      }
    }

    .cs-flower {
      width: 22.625em;
      height: auto;
      display: block;
      position: absolute;
      bottom: -2.375em;
      right: -3em;
      z-index: -1;
      transform: rotate(142deg);
    }

    .cs-sense {
      width: 5em;
      height: auto;
      position: absolute;
      top: -0.25em;
      left: 0.625em;
      transform: rotate(90deg);
    }

    .cs-background {
      display: none;
    }
  }
}

/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #content-page-713 {
    .cs-container {
      flex-direction: row;
      align-items: flex-start;
    }

    .cs-image-group {
      display: block;
    }

    .cs-background {
      width: 20%;
      height: 100%;
      background-color: #f7f7f7;
      display: block;
      position: absolute;
      right: 0;
      top: 0;
      z-index: -1;

      img {
        width: 100%;
        height: 100%;
        opacity: 0.2;
        object-fit: cover;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
      }
    }
  }
}

/* Large Desktop 1300px */
@media only screen and (min-width: 81.25rem) {
  #content-page-713 {
    .cs-image-group {
      /* position absolute so we can have it overflow the container as seen in the design. */
      font-size: inherit;
      position: absolute;
      top: 0rem;
      right: -6.25rem;
    }

    .cs-background {
      width: 50%;

      /* with the left edge always positioned at the center line, we push left of the center line by 335px.  This ensures that this element will stay exactly in this position no matter how wide the screen gets */
      margin-left: 20.9375rem;
      right: auto;

      /* sets the left edge of the element to be at the center line */
      left: 50%;
    }
  }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
  body.dark-mode {
    #content-page-713 {
      background-color: var(--dark);

      .cs-title,
      .cs-text,
      h2,
      h3,
      h4,
      h5,
      h6,
      li,
      p {
        color: var(--bodyTextColorWhite);
      }

      .cs-color,
      a {
        color: var(--primaryLight);
      }

      p,
      li {
        color: #ebebeb;
      }

      .cs-picture {
        border-color: var(--dark);
        background-color: var(--dark);
      }

      .cs-flower {
        filter: brightness(50%);
      }

      .cs-sense {
        filter: brightness(150%);
      }

      .cs-background {
        background-color: var(--medium);
        filter: brightness(70%);

        img {
          opacity: 0.1;
        }
      }
    }
  }
}

/*-- -------------------------- -->
<---          Services          -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #services-367 {
    padding: var(--sectionPadding);

    @keyframes floatAnimation2 {
      0% {
        transform: translateY(0);
      }

      50% {
        transform: translateY(-4px);
      }

      70% {
        transform: translateY(4px);
      }

      100% {
        transform: translateY(0);
      }
    }

    @keyframes InAndOut {
      0% {
        transform: translateY(calc(60 / 16 * 1rem)) scale(1);
        opacity: 1;
      }

      30% {
        transform: translateY(calc(30 / 16 * 1rem)) scale(0.8);
        opacity: 1;
      }

      60% {
        transform: translateY(calc(10 / 16 * 1rem)) scale(0.5);
        opacity: 0.2;
      }

      100% {
        transform: translateY(calc(0 / 16 * 1rem)) scale(0.2);
        opacity: 0;
      }
    }

    @keyframes InAndOut2 {
      0% {
        transform: translateY(calc(70 / 16 * 1rem)) scale(1);
        opacity: 1;
      }

      30% {
        transform: translateY(calc(40 / 16 * 1rem)) scale(0.8);
        opacity: 1;
      }

      60% {
        transform: translateY(calc(20 / 16 * 1rem)) scale(0.6);
        opacity: 1;
      }

      100% {
        transform: translateY(calc(0 / 16 * 1rem)) scale(0.4);
        opacity: 0;
      }
    }

    .cs-container {
      width: 100%;
      /* changes to 1440px at desktop */
      max-width: calc(944 / 16 * 1rem);
      margin: auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      /* 48px - 64px */
      gap: clamp(3rem, 6vw, 4rem);
    }

    .cs-content {
      /* set text align to left if content needs to be left aligned */
      text-align: center;
      width: 100%;
      display: flex;
      flex-direction: column;
      /* centers content horizontally, set to flex-start to left align */
      align-items: center;
    }

    .cs-card-group {
      width: 100%;
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      flex-direction: column;
      /* 16px - 20px */
      gap: clamp(1rem, 2vw, 1.25rem);
    }

    .cs-item {
      list-style: none;
      width: 100%;
      max-width: calc(500 / 16 * 1rem);
      /* 20px - 40px top & bottom */
      /* 16px - 32px left & right */
      padding: clamp(1.25rem, 3.5vw, 2.5rem) clamp(1rem, 2.5vw, 2rem);
      border-radius: calc(16 / 16 * 1rem);
      border: 1px solid #e8e8e8;
      box-sizing: border-box;
      transition: background-color 0.3s;

      &:hover {
        background-color: var(--primary);

        .cs-icon-wrapper,
        .cs-bubble {

          &:before,
          &:after {
            background-color: #449ffc;
          }
        }

        .cs-icon {
          /* makes it white on hover */
          filter: grayscale(1) brightness(1000%);
        }

        .cs-h3 {
          color: #fff;
        }

        .cs-item-text {
          color: #fff;
        }
      }
    }

    .cs-link {
      text-decoration: none;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .cs-icon-wrapper {
      width: calc(98 / 16 * 1rem);
      height: calc(120 / 16 * 1rem);
      margin-bottom: calc(24 / 16 * 1rem);
      display: flex;
      justify-content: center;
      align-items: flex-end;
      position: relative;

      &:before {
        /* Small Bubble */
        content: "";
        width: calc(27 / 16 * 1rem);
        height: calc(27 / 16 * 1rem);
        border-radius: 50%;
        background: #e5f2ff;
        opacity: 1;
        position: absolute;
        display: block;
        top: 0;
        left: calc(3 / 16 * 1rem);
        animation-name: InAndOut;
        animation-duration: 1.6s;
        animation-timing-function: linear;
        animation-iteration-count: infinite;
        transition: background-color 0.3s;
      }

      &:after {
        /* Small Bubble */
        content: "";
        width: calc(27 / 16 * 1rem);
        height: calc(27 / 16 * 1rem);
        border-radius: 50%;
        background: #e5f2ff;
        opacity: 1;
        position: absolute;
        display: block;
        top: calc(11 / 16 * 1rem);
        right: calc(5 / 16 * 1rem);
        animation-name: InAndOut2;
        animation-duration: 2s;
        animation-delay: 0.3s;
        animation-timing-function: linear;
        animation-iteration-count: infinite;
        transition: background-color 0.3s;
      }
    }

    .cs-bubble {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;

      &:before {
        /* Big Bubble */
        content: "";
        width: calc(63 / 16 * 1rem);
        height: calc(63 / 16 * 1rem);
        border-radius: 50%;
        background: #e5f2ff;
        opacity: 1;
        position: absolute;
        display: block;
        bottom: calc(4 / 16 * 1rem);
        left: calc(0 / 16 * 1rem);
        animation-name: floatAnimation2;
        animation-duration: 12s;
        animation-timing-function: ease-in-out;
        animation-fill-mode: forwards;
        animation-iteration-count: infinite;
        transition: background-color 0.3s;
      }

      &:after {
        /* Big Bubble */
        content: "";
        width: calc(50 / 16 * 1rem);
        height: calc(50 / 16 * 1rem);
        border-radius: 50%;
        background: #e5f2ff;
        opacity: 1;
        position: absolute;
        display: block;
        bottom: calc(-4 / 16 * 1rem);
        right: calc(0 / 16 * 1rem);
        animation-name: floatAnimation2;
        animation-duration: 10s;
        animation-timing-function: ease-in-out;
        animation-fill-mode: forwards;
        animation-iteration-count: infinite;
        transition: background-color 0.3s;
      }
    }

    .cs-icon {
      width: calc(72 / 16 * 1rem);
      height: auto;
      position: relative;
      z-index: 10;
    }

    .cs-h3 {
      font-size: calc(20 / 16 * 1rem);
      font-weight: 900;
      text-align: center;
      line-height: 1.2em;
      margin: 0;
      margin-bottom: calc(12 / 16 * 1rem);
      color: var(--headerColor);
      transition: color 0.3s;
    }

    .cs-item-text {
      /* 14px - 16px */
      font-size: clamp(0.875rem, 1.5vw, 1rem);
      text-align: center;
      line-height: 1.5em;
      margin: 0;
      color: var(--bodyTextColor);
      transition: color 0.3s;
    }

    .cs-button-solid {
      font-size: calc(16 / 16 * 1rem);
      /* 46px - 56px */
      line-height: clamp(2.875rem, 5.5vw, 3.5rem);
      text-decoration: none;
      font-weight: 700;
      text-align: center;
      margin: 0;
      color: #fff;
      min-width: calc(150 / 16 * 1rem);
      padding: 0 calc(24 / 16 * 1rem);
      background-color: var(--primary);
      border-radius: calc(4 / 16 * 1rem);
      display: inline-block;
      position: relative;
      z-index: 1;
      /* prevents padding from adding to the width */
      box-sizing: border-box;

      &:before {
        content: "";
        position: absolute;
        height: 100%;
        width: 0%;
        background: #000;
        opacity: 1;
        top: 0;
        left: 0;
        z-index: -1;
        border-radius: calc(4 / 16 * 1rem);
        transition: width 0.3s;
      }

      &:hover {
        &:before {
          width: 100%;
        }
      }
    }
  }
}

/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #services-367 {
    .cs-card-group {
      flex-direction: row;
      justify-content: center;
    }

    .cs-item {
      width: 48%;
    }
  }
}

/* Desktop - 1200px */
@media only screen and (min-width: 75rem) {
  #services-367 {
    .cs-container {
      max-width: calc(1440 / 16 * 1rem);
    }

    .cs-item {
      /* we do this so it's stackable. You can add any number of reviews you want and they will stack and center in the middle. We dont use grid because if you have an odd number of cards, they don't stay centered, they align with their grid lines. This way its more FLEX-ible*/
      width: clamp(23.47%, 23vw, 23.955%);
    }
  }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
  body.dark-mode {
    #services-367 {
      .cs-topper {
        color: var(--primaryLight);
      }

      .cs-title,
      .cs-text,
      .cs-h3,
      .cs-item-text {
        color: var(--bodyTextColorWhite);
      }

      .cs-icon-wrapper,
      .cs-bubble {

        &:before,
        &:after {
          background-color: var(--accent);
        }
      }

      .cs-icon {
        /* turns the icon white */
        filter: grayscale(1) brightness(1000%);
      }
    }
  }
}