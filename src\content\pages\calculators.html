---
title: "Calculators | TopTaxHQ Accounting"
description: "Multiple calculators to help you with your accounting needs including income tax return, loan & mortgage, P&L and more."
preloadImg: "/assets/images/banner-sm.webp"
permalink: "calculators/"
---

{% extends "layouts/base.html" %}

{% block head %}
    <link rel="stylesheet" href="/assets/css/calculator.css" />
{% endblock %}

{% block body %}
    <!-- ============================================ -->
    <!--                   Banner                     -->
    <!-- ============================================ -->

    <div id="banner-843">
        <div class="cs-container">
            <h1 class="cs-int-title">Calculators</h1>
        </div>
        <!--Background Image-->
        <picture class="cs-background" aria-hidden="true">
            <!--Mobile Image-->
            <source
                media="(max-width: 600px)"
                srcset="/assets/images/banner-sm.webp"
            />
            <!--Tablet and above Image-->
            <source
                media="(min-width: 601px)"
                srcset="/assets/images/banner.webp"
            />
            <img
                loading="lazy"
                decoding="async"
                src="/assets/images/banner.webp"
                alt="TopTaxHQ Accounting banner"
                width="1280"
                height="568"
            />
        </picture>
        <!--Change the svg path fill color to whatever color the section below is so you can match it and create the illusion it is all one piece-->
        <svg
            class="cs-wave"
            width="1920"
            height="179"
            viewBox="0 0 1920 179"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M1920 179V91.3463C1835.33 91.3463 1715.47 76.615 1549.2 32.9521C1299.48 -32.3214 1132.77 12.1006 947.32 61.5167C810.762 97.9044 664.***********.533 137C331.***********.468 123.447 188.082 111.113C130.974 100.812 78.5746 91.3609 0 91.3609V179H1920Z"
                fill="white"
            />
        </svg>
    </div>

    <!-- ============================================ -->
    <!--                 Calculator Section           -->
    <!-- ============================================ -->

    <section id="calculator-section">
        <div class="cs-container">
            <!-- Tab Navigation -->
            <div class="cs-tab-nav">
                <div class="cs-tab-track">
                    <!-- First set of buttons -->
                    <button
                        class="cs-tab-button active"
                        data-tab="tax-calculator"
                    >
                        Tax Calculator
                    </button>
                    <button class="cs-tab-button" data-tab="income-tax-return">
                        Income Tax Return
                    </button>
                    <button class="cs-tab-button" data-tab="loan-mortgage">
                        Loan & Mortgage
                    </button>
                    <button class="cs-tab-button" data-tab="profit-loss">
                        Business P&L
                    </button>
                    <button class="cs-tab-button" data-tab="payroll">
                        Payroll Calculator
                    </button>
                    <button class="cs-tab-button" data-tab="self-employment">
                        Self-Employment Tax
                    </button>
                    <button class="cs-tab-button" data-tab="retirement">
                        Retirement Savings
                    </button>
                    <button class="cs-tab-button" data-tab="budget">
                        Budget & Expense
                    </button>
                    <button class="cs-tab-button" data-tab="depreciation">
                        Depreciation
                    </button>
                    <button class="cs-tab-button" data-tab="estimated-tax">
                        Estimated Tax Payment
                    </button>
                    <button class="cs-tab-button" data-tab="mileage">
                        Business Mileage
                    </button>
                    <button class="cs-tab-button" data-tab="sales-tax">
                        VAT/Sales Tax
                    </button>
                    <button class="cs-tab-button" data-tab="cost-capital">
                        Cost of Capital
                    </button>

                    <!-- Duplicate set for infinite scroll -->
                    <button class="cs-tab-button" data-tab="tax-calculator">
                        Tax Calculator
                    </button>
                    <button class="cs-tab-button" data-tab="income-tax-return">
                        Income Tax Return
                    </button>
                    <button class="cs-tab-button" data-tab="loan-mortgage">
                        Loan & Mortgage
                    </button>
                    <button class="cs-tab-button" data-tab="profit-loss">
                        Business P&L
                    </button>
                    <button class="cs-tab-button" data-tab="payroll">
                        Payroll Calculator
                    </button>
                    <button class="cs-tab-button" data-tab="self-employment">
                        Self-Employment Tax
                    </button>
                    <button class="cs-tab-button" data-tab="retirement">
                        Retirement Savings
                    </button>
                    <button class="cs-tab-button" data-tab="budget">
                        Budget & Expense
                    </button>
                    <button class="cs-tab-button" data-tab="depreciation">
                        Depreciation
                    </button>
                    <button class="cs-tab-button" data-tab="estimated-tax">
                        Estimated Tax Payment
                    </button>
                    <button class="cs-tab-button" data-tab="mileage">
                        Business Mileage
                    </button>
                    <button class="cs-tab-button" data-tab="sales-tax">
                        VAT/Sales Tax
                    </button>
                    <button class="cs-tab-button" data-tab="cost-capital">
                        Cost of Capital
                    </button>
                </div>
            </div>

            <!-- Calculator Container -->
            <div class="cs-calculator-container">
                <!-- Tax Calculator -->
                <div class="cs-calculator active" id="tax-calculator">
                    <h2 class="cs-calculator-title">Tax Calculator</h2>
                    <p class="cs-calculator-description">
                        Estimate federal, state, or local taxes based on income,
                        deductions, and credits.
                    </p>
                    <form class="cs-calculator-form">
                        <div class="cs-input-group">
                            <label for="tax-income">Annual Income ($)</label>
                            <input
                                type="number"
                                id="tax-income"
                                name="tax-income"
                                placeholder="Enter your annual income"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="tax-filing-status">Filing Status</label>
                            <select
                                id="tax-filing-status"
                                name="tax-filing-status"
                            >
                                <option value="single">Single</option>
                                <option value="married-joint">
                                    Married Filing Jointly
                                </option>
                                <option value="married-separate">
                                    Married Filing Separately
                                </option>
                                <option value="head-household">
                                    Head of Household
                                </option>
                            </select>
                        </div>
                        <div class="cs-input-group">
                            <label for="tax-deductions"
                                >Standard/Itemized Deductions ($)</label
                            >
                            <input
                                type="number"
                                id="tax-deductions"
                                name="tax-deductions"
                                placeholder="Enter deductions"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="tax-credits">Tax Credits ($)</label>
                            <input
                                type="number"
                                id="tax-credits"
                                name="tax-credits"
                                placeholder="Enter tax credits"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="state-tax-rate"
                                >State Tax Rate (%)</label
                            >
                            <input
                                type="number"
                                id="state-tax-rate"
                                name="state-tax-rate"
                                step="0.01"
                                placeholder="Enter state tax rate"
                            />
                        </div>
                        <button
                            type="button"
                            class="cs-calculate-btn"
                            onclick="calculateTax()"
                        >
                            Calculate Tax
                        </button>
                    </form>
                    <div class="cs-results" id="tax-results">
                        <h3>Tax Calculation Results</h3>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Taxable Income:</span>
                            <span
                                class="cs-result-value"
                                id="tax-taxable-income"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Federal Tax:</span>
                            <span class="cs-result-value" id="tax-federal-tax"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label">State Tax:</span>
                            <span class="cs-result-value" id="tax-state-tax"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Total Tax Liability:</span
                            >
                            <span class="cs-result-value" id="tax-total-tax"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >After-Tax Income:</span
                            >
                            <span
                                class="cs-result-value"
                                id="tax-after-tax-income"
                                >$0</span
                            >
                        </div>
                    </div>
                </div>

                <!-- Income Tax Return Calculator -->
                <div class="cs-calculator" id="income-tax-return">
                    <h2 class="cs-calculator-title">
                        Income Tax Return Calculator
                    </h2>
                    <p class="cs-calculator-description">
                        Estimate your potential tax refund or amount owed based
                        on your financial data.
                    </p>
                    <form class="cs-calculator-form">
                        <div class="cs-input-group">
                            <label for="return-income">Total Income ($)</label>
                            <input
                                type="number"
                                id="return-income"
                                name="return-income"
                                placeholder="Enter total income"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="return-withholding"
                                >Federal Tax Withheld ($)</label
                            >
                            <input
                                type="number"
                                id="return-withholding"
                                name="return-withholding"
                                placeholder="Enter tax withheld"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="return-deductions"
                                >Deductions ($)</label
                            >
                            <input
                                type="number"
                                id="return-deductions"
                                name="return-deductions"
                                placeholder="Enter deductions"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="return-credits">Tax Credits ($)</label>
                            <input
                                type="number"
                                id="return-credits"
                                name="return-credits"
                                placeholder="Enter tax credits"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="return-filing-status"
                                >Filing Status</label
                            >
                            <select
                                id="return-filing-status"
                                name="return-filing-status"
                            >
                                <option value="single">Single</option>
                                <option value="married-joint">
                                    Married Filing Jointly
                                </option>
                                <option value="married-separate">
                                    Married Filing Separately
                                </option>
                                <option value="head-household">
                                    Head of Household
                                </option>
                            </select>
                        </div>
                        <button
                            type="button"
                            class="cs-calculate-btn"
                            onclick="calculateTaxReturn()"
                        >
                            Calculate Return
                        </button>
                    </form>
                    <div class="cs-results" id="return-results">
                        <h3>Tax Return Results</h3>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Taxable Income:</span>
                            <span
                                class="cs-result-value"
                                id="return-taxable-income"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Tax Liability:</span>
                            <span
                                class="cs-result-value"
                                id="return-tax-liability"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Tax Withheld:</span>
                            <span
                                class="cs-result-value"
                                id="return-tax-withheld"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Refund/Amount Owed:</span
                            >
                            <span
                                class="cs-result-value"
                                id="return-refund-owed"
                                >$0</span
                            >
                        </div>
                    </div>
                </div>

                <!-- Loan & Mortgage Calculator -->
                <div class="cs-calculator" id="loan-mortgage">
                    <h2 class="cs-calculator-title">
                        Loan & Mortgage Calculator
                    </h2>
                    <p class="cs-calculator-description">
                        Calculate monthly payments, interest over time, and
                        amortization schedules.
                    </p>
                    <form class="cs-calculator-form">
                        <div class="cs-input-group">
                            <label for="loan-amount">Loan Amount ($)</label>
                            <input
                                type="number"
                                id="loan-amount"
                                name="loan-amount"
                                placeholder="Enter loan amount"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="interest-rate"
                                >Annual Interest Rate (%)</label
                            >
                            <input
                                type="number"
                                id="interest-rate"
                                name="interest-rate"
                                step="0.01"
                                placeholder="Enter interest rate"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="loan-term">Loan Term (years)</label>
                            <input
                                type="number"
                                id="loan-term"
                                name="loan-term"
                                placeholder="Enter loan term"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="down-payment">Down Payment ($)</label>
                            <input
                                type="number"
                                id="down-payment"
                                name="down-payment"
                                placeholder="Enter down payment (optional)"
                            />
                        </div>
                        <button
                            type="button"
                            class="cs-calculate-btn"
                            onclick="calculateLoan()"
                        >
                            Calculate Payment
                        </button>
                    </form>
                    <div class="cs-results" id="loan-results">
                        <h3>Loan Calculation Results</h3>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Monthly Payment:</span
                            >
                            <span class="cs-result-value" id="monthly-payment"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Total Interest:</span>
                            <span class="cs-result-value" id="total-interest"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Total Amount:</span>
                            <span class="cs-result-value" id="total-amount"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Principal & Interest:</span
                            >
                            <span
                                class="cs-result-value"
                                id="principal-interest"
                                >$0</span
                            >
                        </div>
                    </div>
                </div>

                <!-- Business P&L Calculator -->
                <div class="cs-calculator" id="profit-loss">
                    <h2 class="cs-calculator-title">
                        Business Profit & Loss Calculator
                    </h2>
                    <p class="cs-calculator-description">
                        Assist small business owners in estimating profits,
                        expenses, and taxable income.
                    </p>
                    <form class="cs-calculator-form">
                        <div class="cs-input-group">
                            <label for="revenue">Total Revenue ($)</label>
                            <input
                                type="number"
                                id="revenue"
                                name="revenue"
                                placeholder="Enter total revenue"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="cogs">Cost of Goods Sold ($)</label>
                            <input
                                type="number"
                                id="cogs"
                                name="cogs"
                                placeholder="Enter COGS"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="operating-expenses"
                                >Operating Expenses ($)</label
                            >
                            <input
                                type="number"
                                id="operating-expenses"
                                name="operating-expenses"
                                placeholder="Enter operating expenses"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="other-expenses"
                                >Other Expenses ($)</label
                            >
                            <input
                                type="number"
                                id="other-expenses"
                                name="other-expenses"
                                placeholder="Enter other expenses"
                            />
                        </div>
                        <button
                            type="button"
                            class="cs-calculate-btn"
                            onclick="calculateProfitLoss()"
                        >
                            Calculate P&L
                        </button>
                    </form>
                    <div class="cs-results" id="pl-results">
                        <h3>Profit & Loss Results</h3>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Gross Profit:</span>
                            <span class="cs-result-value" id="gross-profit"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Operating Income:</span
                            >
                            <span class="cs-result-value" id="operating-income"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Net Income:</span>
                            <span class="cs-result-value" id="net-income"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Profit Margin:</span>
                            <span class="cs-result-value" id="profit-margin"
                                >0%</span
                            >
                        </div>
                    </div>
                </div>

                <!-- Payroll Calculator -->
                <div class="cs-calculator" id="payroll">
                    <h2 class="cs-calculator-title">Payroll Calculator</h2>
                    <p class="cs-calculator-description">
                        Calculate gross pay, deductions (taxes, benefits), and
                        net pay for employees or contractors.
                    </p>
                    <form class="cs-calculator-form">
                        <div class="cs-input-group">
                            <label for="gross-pay">Gross Pay ($)</label>
                            <input
                                type="number"
                                id="gross-pay"
                                name="gross-pay"
                                placeholder="Enter gross pay"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="federal-withholding"
                                >Federal Withholding (%)</label
                            >
                            <input
                                type="number"
                                id="federal-withholding"
                                name="federal-withholding"
                                step="0.01"
                                placeholder="Enter federal withholding rate"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="state-withholding"
                                >State Withholding (%)</label
                            >
                            <input
                                type="number"
                                id="state-withholding"
                                name="state-withholding"
                                step="0.01"
                                placeholder="Enter state withholding rate"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="social-security"
                                >Social Security (6.2%)</label
                            >
                            <input
                                type="number"
                                id="social-security"
                                name="social-security"
                                value="6.2"
                                step="0.01"
                                readonly
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="medicare">Medicare (1.45%)</label>
                            <input
                                type="number"
                                id="medicare"
                                name="medicare"
                                value="1.45"
                                step="0.01"
                                readonly
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="other-deductions"
                                >Other Deductions ($)</label
                            >
                            <input
                                type="number"
                                id="other-deductions"
                                name="other-deductions"
                                placeholder="Enter other deductions"
                            />
                        </div>
                        <button
                            type="button"
                            class="cs-calculate-btn"
                            onclick="calculatePayroll()"
                        >
                            Calculate Payroll
                        </button>
                    </form>
                    <div class="cs-results" id="payroll-results">
                        <h3>Payroll Results</h3>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Gross Pay:</span>
                            <span class="cs-result-value" id="payroll-gross-pay"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Total Deductions:</span
                            >
                            <span class="cs-result-value" id="total-deductions"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Net Pay:</span>
                            <span class="cs-result-value" id="net-pay">$0</span>
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Take-Home Percentage:</span
                            >
                            <span
                                class="cs-result-value"
                                id="take-home-percentage"
                                >0%</span
                            >
                        </div>
                    </div>
                </div>

                <!-- Self-Employment Tax Calculator -->
                <div class="cs-calculator" id="self-employment">
                    <h2 class="cs-calculator-title">
                        Self-Employment Tax Calculator
                    </h2>
                    <p class="cs-calculator-description">
                        Estimate self-employment taxes owed based on income.
                    </p>
                    <form class="cs-calculator-form">
                        <div class="cs-input-group">
                            <label for="se-income"
                                >Self-Employment Income ($)</label
                            >
                            <input
                                type="number"
                                id="se-income"
                                name="se-income"
                                placeholder="Enter self-employment income"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="se-expenses"
                                >Business Expenses ($)</label
                            >
                            <input
                                type="number"
                                id="se-expenses"
                                name="se-expenses"
                                placeholder="Enter business expenses"
                            />
                        </div>
                        <button
                            type="button"
                            class="cs-calculate-btn"
                            onclick="calculateSelfEmploymentTax()"
                        >
                            Calculate SE Tax
                        </button>
                    </form>
                    <div class="cs-results" id="se-results">
                        <h3>Self-Employment Tax Results</h3>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Net Self-Employment Income:</span
                            >
                            <span class="cs-result-value" id="se-net-income"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Self-Employment Tax:</span
                            >
                            <span class="cs-result-value" id="se-tax">$0</span>
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Deductible Portion:</span
                            >
                            <span class="cs-result-value" id="se-deductible"
                                >$0</span
                            >
                        </div>
                    </div>
                </div>

                <!-- Retirement Savings Calculator -->
                <div class="cs-calculator" id="retirement">
                    <h2 class="cs-calculator-title">
                        Retirement Savings Calculator
                    </h2>
                    <p class="cs-calculator-description">
                        Plan for retirement by projecting future savings based
                        on current contributions and growth rates.
                    </p>
                    <form class="cs-calculator-form">
                        <div class="cs-input-group">
                            <label for="current-age">Current Age</label>
                            <input
                                type="number"
                                id="current-age"
                                name="current-age"
                                placeholder="Enter your current age"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="retirement-age">Retirement Age</label>
                            <input
                                type="number"
                                id="retirement-age"
                                name="retirement-age"
                                placeholder="Enter retirement age"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="current-savings"
                                >Current Savings ($)</label
                            >
                            <input
                                type="number"
                                id="current-savings"
                                name="current-savings"
                                placeholder="Enter current savings"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="monthly-contribution"
                                >Monthly Contribution ($)</label
                            >
                            <input
                                type="number"
                                id="monthly-contribution"
                                name="monthly-contribution"
                                placeholder="Enter monthly contribution"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="annual-return"
                                >Expected Annual Return (%)</label
                            >
                            <input
                                type="number"
                                id="annual-return"
                                name="annual-return"
                                step="0.01"
                                placeholder="Enter expected return rate"
                            />
                        </div>
                        <button
                            type="button"
                            class="cs-calculate-btn"
                            onclick="calculateRetirement()"
                        >
                            Calculate Retirement
                        </button>
                    </form>
                    <div class="cs-results" id="retirement-results">
                        <h3>Retirement Projection Results</h3>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Years to Retirement:</span
                            >
                            <span
                                class="cs-result-value"
                                id="years-to-retirement"
                                >0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Total Contributions:</span
                            >
                            <span
                                class="cs-result-value"
                                id="total-contributions"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Projected Balance:</span
                            >
                            <span class="cs-result-value" id="projected-balance"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Investment Growth:</span
                            >
                            <span class="cs-result-value" id="investment-growth"
                                >$0</span
                            >
                        </div>
                    </div>
                </div>

                <!-- Budget & Expense Calculator -->
                <div class="cs-calculator" id="budget">
                    <h2 class="cs-calculator-title">
                        Budget & Expense Calculator
                    </h2>
                    <p class="cs-calculator-description">
                        Create budgets and track expenses to manage your
                        finances effectively.
                    </p>
                    <form class="cs-calculator-form">
                        <div class="cs-input-group">
                            <label for="monthly-income"
                                >Monthly Income ($)</label
                            >
                            <input
                                type="number"
                                id="monthly-income"
                                name="monthly-income"
                                placeholder="Enter monthly income"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="housing-costs">Housing Costs ($)</label>
                            <input
                                type="number"
                                id="housing-costs"
                                name="housing-costs"
                                placeholder="Enter housing costs"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="transportation"
                                >Transportation ($)</label
                            >
                            <input
                                type="number"
                                id="transportation"
                                name="transportation"
                                placeholder="Enter transportation costs"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="food-expenses">Food & Dining ($)</label>
                            <input
                                type="number"
                                id="food-expenses"
                                name="food-expenses"
                                placeholder="Enter food expenses"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="entertainment">Entertainment ($)</label>
                            <input
                                type="number"
                                id="entertainment"
                                name="entertainment"
                                placeholder="Enter entertainment costs"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="other-budget-expenses"
                                >Other Expenses ($)</label
                            >
                            <input
                                type="number"
                                id="other-budget-expenses"
                                name="other-budget-expenses"
                                placeholder="Enter other expenses"
                            />
                        </div>
                        <button
                            type="button"
                            class="cs-calculate-btn"
                            onclick="calculateBudget()"
                        >
                            Calculate Budget
                        </button>
                    </form>
                    <div class="cs-results" id="budget-results">
                        <h3>Budget Analysis Results</h3>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Total Expenses:</span>
                            <span
                                class="cs-result-value"
                                id="budget-total-expenses"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Remaining Income:</span
                            >
                            <span class="cs-result-value" id="remaining-income"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Savings Rate:</span>
                            <span class="cs-result-value" id="savings-rate"
                                >0%</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Budget Status:</span>
                            <span class="cs-result-value" id="budget-status"
                                >-</span
                            >
                        </div>
                    </div>
                </div>

                <!-- Depreciation Calculator -->
                <div class="cs-calculator" id="depreciation">
                    <h2 class="cs-calculator-title">Depreciation Calculator</h2>
                    <p class="cs-calculator-description">
                        Calculate depreciation for fixed assets according to
                        various methods.
                    </p>
                    <form class="cs-calculator-form">
                        <div class="cs-input-group">
                            <label for="asset-cost">Asset Cost ($)</label>
                            <input
                                type="number"
                                id="asset-cost"
                                name="asset-cost"
                                placeholder="Enter asset cost"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="salvage-value">Salvage Value ($)</label>
                            <input
                                type="number"
                                id="salvage-value"
                                name="salvage-value"
                                placeholder="Enter salvage value"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="useful-life">Useful Life (years)</label>
                            <input
                                type="number"
                                id="useful-life"
                                name="useful-life"
                                placeholder="Enter useful life"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="depreciation-method"
                                >Depreciation Method</label
                            >
                            <select
                                id="depreciation-method"
                                name="depreciation-method"
                            >
                                <option value="straight-line">
                                    Straight Line
                                </option>
                                <option value="declining-balance">
                                    Declining Balance
                                </option>
                                <option value="sum-of-years">
                                    Sum of Years' Digits
                                </option>
                            </select>
                        </div>
                        <button
                            type="button"
                            class="cs-calculate-btn"
                            onclick="calculateDepreciation()"
                        >
                            Calculate Depreciation
                        </button>
                    </form>
                    <div class="cs-results" id="depreciation-results">
                        <h3>Depreciation Results</h3>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Annual Depreciation:</span
                            >
                            <span
                                class="cs-result-value"
                                id="annual-depreciation"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Total Depreciation:</span
                            >
                            <span
                                class="cs-result-value"
                                id="total-depreciation"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Book Value:</span>
                            <span class="cs-result-value" id="book-value"
                                >$0</span
                            >
                        </div>
                    </div>
                </div>

                <!-- Estimated Tax Payment Calculator -->
                <div class="cs-calculator" id="estimated-tax">
                    <h2 class="cs-calculator-title">
                        Estimated Tax Payment Calculator
                    </h2>
                    <p class="cs-calculator-description">
                        Help individuals and businesses estimate quarterly tax
                        payments.
                    </p>
                    <form class="cs-calculator-form">
                        <div class="cs-input-group">
                            <label for="estimated-income"
                                >Estimated Annual Income ($)</label
                            >
                            <input
                                type="number"
                                id="estimated-income"
                                name="estimated-income"
                                placeholder="Enter estimated income"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="estimated-deductions"
                                >Estimated Deductions ($)</label
                            >
                            <input
                                type="number"
                                id="estimated-deductions"
                                name="estimated-deductions"
                                placeholder="Enter estimated deductions"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="prior-year-tax"
                                >Prior Year Tax Liability ($)</label
                            >
                            <input
                                type="number"
                                id="prior-year-tax"
                                name="prior-year-tax"
                                placeholder="Enter prior year tax"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="withholding-credits"
                                >Withholding & Credits ($)</label
                            >
                            <input
                                type="number"
                                id="withholding-credits"
                                name="withholding-credits"
                                placeholder="Enter withholding and credits"
                            />
                        </div>
                        <button
                            type="button"
                            class="cs-calculate-btn"
                            onclick="calculateEstimatedTax()"
                        >
                            Calculate Estimated Tax
                        </button>
                    </form>
                    <div class="cs-results" id="estimated-tax-results">
                        <h3>Estimated Tax Results</h3>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Estimated Tax Liability:</span
                            >
                            <span
                                class="cs-result-value"
                                id="estimated-tax-liability"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Quarterly Payment:</span
                            >
                            <span class="cs-result-value" id="quarterly-payment"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Safe Harbor Amount:</span
                            >
                            <span
                                class="cs-result-value"
                                id="safe-harbor-amount"
                                >$0</span
                            >
                        </div>
                    </div>
                </div>

                <!-- Business Mileage Calculator -->
                <div class="cs-calculator" id="mileage">
                    <h2 class="cs-calculator-title">
                        Business Mileage Deduction Calculator
                    </h2>
                    <p class="cs-calculator-description">
                        Calculate deductible mileage costs for business-related
                        travel.
                    </p>
                    <form class="cs-calculator-form">
                        <div class="cs-input-group">
                            <label for="business-miles"
                                >Business Miles Driven</label
                            >
                            <input
                                type="number"
                                id="business-miles"
                                name="business-miles"
                                placeholder="Enter business miles"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="mileage-rate"
                                >IRS Mileage Rate (per mile)</label
                            >
                            <input
                                type="number"
                                id="mileage-rate"
                                name="mileage-rate"
                                value="0.655"
                                step="0.001"
                                placeholder="Enter mileage rate"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="total-miles">Total Miles Driven</label>
                            <input
                                type="number"
                                id="total-miles"
                                name="total-miles"
                                placeholder="Enter total miles (optional)"
                            />
                        </div>
                        <button
                            type="button"
                            class="cs-calculate-btn"
                            onclick="calculateMileage()"
                        >
                            Calculate Mileage Deduction
                        </button>
                    </form>
                    <div class="cs-results" id="mileage-results">
                        <h3>Mileage Deduction Results</h3>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Deductible Amount:</span
                            >
                            <span class="cs-result-value" id="mileage-deduction"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Business Use Percentage:</span
                            >
                            <span
                                class="cs-result-value"
                                id="business-percentage"
                                >0%</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label"
                                >Annual Deduction:</span
                            >
                            <span
                                class="cs-result-value"
                                id="annual-mileage-deduction"
                                >$0</span
                            >
                        </div>
                    </div>
                </div>

                <!-- VAT/Sales Tax Calculator -->
                <div class="cs-calculator" id="sales-tax">
                    <h2 class="cs-calculator-title">
                        VAT/Sales Tax Calculator
                    </h2>
                    <p class="cs-calculator-description">
                        Compute sales tax for transactions, useful for
                        businesses dealing with sales in different regions.
                    </p>
                    <form class="cs-calculator-form">
                        <div class="cs-input-group">
                            <label for="sale-amount">Sale Amount ($)</label>
                            <input
                                type="number"
                                id="sale-amount"
                                name="sale-amount"
                                placeholder="Enter sale amount"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="tax-rate">Tax Rate (%)</label>
                            <input
                                type="number"
                                id="tax-rate"
                                name="tax-rate"
                                step="0.01"
                                placeholder="Enter tax rate"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="tax-inclusive">Tax Calculation</label>
                            <select id="tax-inclusive" name="tax-inclusive">
                                <option value="exclusive">
                                    Tax Exclusive (add tax to amount)
                                </option>
                                <option value="inclusive">
                                    Tax Inclusive (extract tax from amount)
                                </option>
                            </select>
                        </div>
                        <button
                            type="button"
                            class="cs-calculate-btn"
                            onclick="calculateSalesTax()"
                        >
                            Calculate Sales Tax
                        </button>
                    </form>
                    <div class="cs-results" id="sales-tax-results">
                        <h3>Sales Tax Results</h3>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Net Amount:</span>
                            <span class="cs-result-value" id="net-amount"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Tax Amount:</span>
                            <span class="cs-result-value" id="tax-amount"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Total Amount:</span>
                            <span
                                class="cs-result-value"
                                id="sales-total-amount"
                                >$0</span
                            >
                        </div>
                    </div>
                </div>

                <!-- Cost of Capital Calculator -->
                <div class="cs-calculator" id="cost-capital">
                    <h2 class="cs-calculator-title">
                        Cost of Capital Calculator
                    </h2>
                    <p class="cs-calculator-description">
                        For businesses evaluating financing options or
                        investments.
                    </p>
                    <form class="cs-calculator-form">
                        <div class="cs-input-group">
                            <label for="debt-amount">Total Debt ($)</label>
                            <input
                                type="number"
                                id="debt-amount"
                                name="debt-amount"
                                placeholder="Enter total debt"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="debt-rate">Cost of Debt (%)</label>
                            <input
                                type="number"
                                id="debt-rate"
                                name="debt-rate"
                                step="0.01"
                                placeholder="Enter cost of debt"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="equity-amount">Total Equity ($)</label>
                            <input
                                type="number"
                                id="equity-amount"
                                name="equity-amount"
                                placeholder="Enter total equity"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="equity-rate">Cost of Equity (%)</label>
                            <input
                                type="number"
                                id="equity-rate"
                                name="equity-rate"
                                step="0.01"
                                placeholder="Enter cost of equity"
                            />
                        </div>
                        <div class="cs-input-group">
                            <label for="tax-rate-wacc">Tax Rate (%)</label>
                            <input
                                type="number"
                                id="tax-rate-wacc"
                                name="tax-rate-wacc"
                                step="0.01"
                                placeholder="Enter tax rate"
                            />
                        </div>
                        <button
                            type="button"
                            class="cs-calculate-btn"
                            onclick="calculateCostOfCapital()"
                        >
                            Calculate WACC
                        </button>
                    </form>
                    <div class="cs-results" id="cost-capital-results">
                        <h3>Cost of Capital Results</h3>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Total Capital:</span>
                            <span class="cs-result-value" id="total-capital"
                                >$0</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Debt Weight:</span>
                            <span class="cs-result-value" id="debt-weight"
                                >0%</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label">Equity Weight:</span>
                            <span class="cs-result-value" id="equity-weight"
                                >0%</span
                            >
                        </div>
                        <div class="cs-result-item">
                            <span class="cs-result-label">WACC:</span>
                            <span class="cs-result-value" id="wacc">0%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Calculator JavaScript -->
    <script>
        // Tab switching functionality
        document.addEventListener("DOMContentLoaded", function () {
            const tabButtons = document.querySelectorAll(".cs-tab-button");
            const calculators = document.querySelectorAll(".cs-calculator");
            const tabTrack = document.querySelector(".cs-tab-track");

            tabButtons.forEach((button) => {
                button.addEventListener("click", function () {
                    const targetTab = this.getAttribute("data-tab");

                    // Remove active class from all buttons and calculators
                    tabButtons.forEach((btn) => btn.classList.remove("active"));
                    calculators.forEach((calc) =>
                        calc.classList.remove("active"),
                    );

                    // Add active class to clicked button and corresponding calculator
                    this.classList.add("active");
                    document.getElementById(targetTab).classList.add("active");
                });
            });

            // Enhanced carousel controls
            if (tabTrack) {
                let currentTransform = 0;
                let isAnimating = true;
                let animationSpeed = 60; // seconds for full cycle

                // Pause animation on hover for better UX
                tabTrack.addEventListener("mouseenter", function () {
                    this.style.animationPlayState = "paused";
                });

                tabTrack.addEventListener("mouseleave", function () {
                    if (isAnimating) {
                        this.style.animationPlayState = "running";
                    }
                });

                // Mouse wheel scrolling for manual control
                tabTrack.addEventListener("wheel", function(e) {
                    e.preventDefault();

                    // Pause automatic animation when user scrolls
                    this.style.animationPlayState = "paused";
                    isAnimating = false;

                    // Calculate scroll amount (adjust speed as needed)
                    const scrollSpeed = 3;
                    currentTransform -= e.deltaY * scrollSpeed;

                    // Get the width of one complete set (13 buttons)
                    const buttonWidth = 196; // 180px + 16px gap
                    const oneSetWidth = buttonWidth * 13;

                    // Keep transform within bounds for seamless loop
                    if (currentTransform <= -oneSetWidth) {
                        currentTransform = 0;
                    } else if (currentTransform > 0) {
                        currentTransform = -oneSetWidth;
                    }

                    // Apply the transform
                    this.style.transform = `translateX(${currentTransform}px)`;
                    this.style.animation = "none";

                    // Resume automatic animation after a delay
                    clearTimeout(this.resumeTimeout);
                    this.resumeTimeout = setTimeout(() => {
                        // Reset to CSS animation from current position
                        const progress = Math.abs(currentTransform) / oneSetWidth;
                        const remainingTime = animationSpeed * (1 - progress);

                        this.style.animation = `infiniteScroll ${remainingTime}s linear infinite`;
                        this.style.animationDelay = "0s";
                        isAnimating = true;
                    }, 2000); // Resume after 2 seconds of no scrolling

                }, { passive: false });
            }
        });

        // Tax Calculator
        function calculateTax() {
            const income =
                parseFloat(document.getElementById("tax-income").value) || 0;
            const deductions =
                parseFloat(document.getElementById("tax-deductions").value) ||
                0;
            const credits =
                parseFloat(document.getElementById("tax-credits").value) || 0;
            const stateRate =
                parseFloat(document.getElementById("state-tax-rate").value) ||
                0;
            const filingStatus =
                document.getElementById("tax-filing-status").value;

            const taxableIncome = Math.max(0, income - deductions);
            let federalTax = calculateFederalTax(taxableIncome, filingStatus);
            federalTax = Math.max(0, federalTax - credits);

            const stateTax = taxableIncome * (stateRate / 100);
            const totalTax = federalTax + stateTax;
            const afterTaxIncome = income - totalTax;

            document.getElementById("tax-taxable-income").textContent =
                "$" + taxableIncome.toLocaleString();
            document.getElementById("tax-federal-tax").textContent =
                "$" + federalTax.toLocaleString();
            document.getElementById("tax-state-tax").textContent =
                "$" + stateTax.toLocaleString();
            document.getElementById("tax-total-tax").textContent =
                "$" + totalTax.toLocaleString();
            document.getElementById("tax-after-tax-income").textContent =
                "$" + afterTaxIncome.toLocaleString();
        }

        // Income Tax Return Calculator
        function calculateTaxReturn() {
            const income =
                parseFloat(document.getElementById("return-income").value) || 0;
            const withholding =
                parseFloat(
                    document.getElementById("return-withholding").value,
                ) || 0;
            const deductions =
                parseFloat(
                    document.getElementById("return-deductions").value,
                ) || 0;
            const credits =
                parseFloat(document.getElementById("return-credits").value) ||
                0;
            const filingStatus = document.getElementById(
                "return-filing-status",
            ).value;

            const taxableIncome = Math.max(0, income - deductions);
            let taxLiability = calculateFederalTax(taxableIncome, filingStatus);
            taxLiability = Math.max(0, taxLiability - credits);

            const refundOrOwed = withholding - taxLiability;

            document.getElementById("return-taxable-income").textContent =
                "$" + taxableIncome.toLocaleString();
            document.getElementById("return-tax-liability").textContent =
                "$" + taxLiability.toLocaleString();
            document.getElementById("return-tax-withheld").textContent =
                "$" + withholding.toLocaleString();
            document.getElementById("return-refund-owed").textContent =
                (refundOrOwed >= 0 ? "Refund: $" : "Owed: $") +
                Math.abs(refundOrOwed).toLocaleString();
        }

        // Helper function for federal tax calculation
        function calculateFederalTax(taxableIncome, filingStatus) {
            let federalTax = 0;

            // 2023 tax brackets for single filers (simplified)
            if (filingStatus === "single") {
                if (taxableIncome > 578125)
                    federalTax += (taxableIncome - 578125) * 0.37;
                if (taxableIncome > 231250)
                    federalTax +=
                        Math.min(taxableIncome - 231250, 346875) * 0.35;
                if (taxableIncome > 182050)
                    federalTax +=
                        Math.min(taxableIncome - 182050, 49200) * 0.32;
                if (taxableIncome > 95450)
                    federalTax += Math.min(taxableIncome - 95450, 86600) * 0.24;
                if (taxableIncome > 44725)
                    federalTax += Math.min(taxableIncome - 44725, 50725) * 0.22;
                if (taxableIncome > 11000)
                    federalTax += Math.min(taxableIncome - 11000, 33725) * 0.12;
                if (taxableIncome > 0)
                    federalTax += Math.min(taxableIncome, 11000) * 0.1;
            }
            // Add other filing statuses as needed

            return federalTax;
        }

        // Loan Calculator
        function calculateLoan() {
            const loanAmount =
                parseFloat(document.getElementById("loan-amount").value) || 0;
            const downPayment =
                parseFloat(document.getElementById("down-payment").value) || 0;
            const principal = loanAmount - downPayment;
            const annualRate =
                parseFloat(document.getElementById("interest-rate").value) || 0;
            const years =
                parseFloat(document.getElementById("loan-term").value) || 0;

            const monthlyRate = annualRate / 100 / 12;
            const numPayments = years * 12;

            let monthlyPayment = 0;
            if (monthlyRate > 0) {
                monthlyPayment =
                    (principal *
                        (monthlyRate *
                            Math.pow(1 + monthlyRate, numPayments))) /
                    (Math.pow(1 + monthlyRate, numPayments) - 1);
            } else {
                monthlyPayment = principal / numPayments;
            }

            const totalAmount = monthlyPayment * numPayments;
            const totalInterest = totalAmount - principal;

            document.getElementById("monthly-payment").textContent =
                "$" + monthlyPayment.toFixed(2);
            document.getElementById("total-interest").textContent =
                "$" + totalInterest.toLocaleString();
            document.getElementById("total-amount").textContent =
                "$" + totalAmount.toLocaleString();
            document.getElementById("principal-interest").textContent =
                "$" + principal.toLocaleString();
        }

        // P&L Calculator
        function calculateProfitLoss() {
            const revenue =
                parseFloat(document.getElementById("revenue").value) || 0;
            const cogs = parseFloat(document.getElementById("cogs").value) || 0;
            const operatingExpenses =
                parseFloat(
                    document.getElementById("operating-expenses").value,
                ) || 0;
            const otherExpenses =
                parseFloat(document.getElementById("other-expenses").value) ||
                0;

            const grossProfit = revenue - cogs;
            const operatingIncome = grossProfit - operatingExpenses;
            const netIncome = operatingIncome - otherExpenses;
            const profitMargin = revenue > 0 ? (netIncome / revenue) * 100 : 0;

            document.getElementById("gross-profit").textContent =
                "$" + grossProfit.toLocaleString();
            document.getElementById("operating-income").textContent =
                "$" + operatingIncome.toLocaleString();
            document.getElementById("net-income").textContent =
                "$" + netIncome.toLocaleString();
            document.getElementById("profit-margin").textContent =
                profitMargin.toFixed(2) + "%";
        }

        // Payroll Calculator
        function calculatePayroll() {
            const grossPay =
                parseFloat(document.getElementById("gross-pay").value) || 0;
            const federalRate =
                parseFloat(
                    document.getElementById("federal-withholding").value,
                ) || 0;
            const stateRate =
                parseFloat(
                    document.getElementById("state-withholding").value,
                ) || 0;
            const socialSecurityRate = 6.2;
            const medicareRate = 1.45;
            const otherDeductions =
                parseFloat(document.getElementById("other-deductions").value) ||
                0;

            const federalWithholding = grossPay * (federalRate / 100);
            const stateWithholding = grossPay * (stateRate / 100);
            const socialSecurity = grossPay * (socialSecurityRate / 100);
            const medicare = grossPay * (medicareRate / 100);

            const totalDeductions =
                federalWithholding +
                stateWithholding +
                socialSecurity +
                medicare +
                otherDeductions;
            const netPay = grossPay - totalDeductions;
            const takeHomePercentage =
                grossPay > 0 ? (netPay / grossPay) * 100 : 0;

            document.getElementById("payroll-gross-pay").textContent =
                "$" + grossPay.toLocaleString();
            document.getElementById("total-deductions").textContent =
                "$" + totalDeductions.toLocaleString();
            document.getElementById("net-pay").textContent =
                "$" + netPay.toLocaleString();
            document.getElementById("take-home-percentage").textContent =
                takeHomePercentage.toFixed(1) + "%";
        }

        // Self-Employment Tax Calculator
        function calculateSelfEmploymentTax() {
            const seIncome =
                parseFloat(document.getElementById("se-income").value) || 0;
            const expenses =
                parseFloat(document.getElementById("se-expenses").value) || 0;

            const netIncome = seIncome - expenses;
            const seTax = netIncome * 0.9235 * 0.153; // 15.3% on 92.35% of net income
            const deductiblePortion = seTax * 0.5; // Half is deductible

            document.getElementById("se-net-income").textContent =
                "$" + netIncome.toLocaleString();
            document.getElementById("se-tax").textContent =
                "$" + seTax.toLocaleString();
            document.getElementById("se-deductible").textContent =
                "$" + deductiblePortion.toLocaleString();
        }

        // Retirement Savings Calculator
        function calculateRetirement() {
            const currentAge =
                parseInt(document.getElementById("current-age").value) || 0;
            const retirementAge =
                parseInt(document.getElementById("retirement-age").value) || 0;
            const currentSavings =
                parseFloat(document.getElementById("current-savings").value) ||
                0;
            const monthlyContribution =
                parseFloat(
                    document.getElementById("monthly-contribution").value,
                ) || 0;
            const annualReturn =
                parseFloat(document.getElementById("annual-return").value) || 0;

            const yearsToRetirement = retirementAge - currentAge;
            const monthlyReturn = annualReturn / 100 / 12;
            const totalMonths = yearsToRetirement * 12;
            const totalContributions = monthlyContribution * totalMonths;

            // Future value calculation
            const futureValueCurrent =
                currentSavings *
                Math.pow(1 + annualReturn / 100, yearsToRetirement);
            const futureValueContributions =
                monthlyContribution *
                ((Math.pow(1 + monthlyReturn, totalMonths) - 1) /
                    monthlyReturn);

            const projectedBalance =
                futureValueCurrent + futureValueContributions;
            const investmentGrowth =
                projectedBalance - currentSavings - totalContributions;

            document.getElementById("years-to-retirement").textContent =
                yearsToRetirement;
            document.getElementById("total-contributions").textContent =
                "$" + totalContributions.toLocaleString();
            document.getElementById("projected-balance").textContent =
                "$" + projectedBalance.toLocaleString();
            document.getElementById("investment-growth").textContent =
                "$" + investmentGrowth.toLocaleString();
        }

        // Budget Calculator
        function calculateBudget() {
            const monthlyIncome =
                parseFloat(document.getElementById("monthly-income").value) ||
                0;
            const housing =
                parseFloat(document.getElementById("housing-costs").value) || 0;
            const transportation =
                parseFloat(document.getElementById("transportation").value) ||
                0;
            const food =
                parseFloat(document.getElementById("food-expenses").value) || 0;
            const entertainment =
                parseFloat(document.getElementById("entertainment").value) || 0;
            const otherExpenses =
                parseFloat(
                    document.getElementById("other-budget-expenses").value,
                ) || 0;

            const totalExpenses =
                housing + transportation + food + entertainment + otherExpenses;
            const remainingIncome = monthlyIncome - totalExpenses;
            const savingsRate =
                monthlyIncome > 0 ? (remainingIncome / monthlyIncome) * 100 : 0;
            const budgetStatus =
                remainingIncome >= 0 ? "On Track" : "Over Budget";

            document.getElementById("budget-total-expenses").textContent =
                "$" + totalExpenses.toLocaleString();
            document.getElementById("remaining-income").textContent =
                "$" + remainingIncome.toLocaleString();
            document.getElementById("savings-rate").textContent =
                savingsRate.toFixed(1) + "%";
            document.getElementById("budget-status").textContent = budgetStatus;
        }

        // Depreciation Calculator
        function calculateDepreciation() {
            const assetCost =
                parseFloat(document.getElementById("asset-cost").value) || 0;
            const salvageValue =
                parseFloat(document.getElementById("salvage-value").value) || 0;
            const usefulLife =
                parseFloat(document.getElementById("useful-life").value) || 0;
            const method = document.getElementById("depreciation-method").value;

            let annualDepreciation = 0;
            const depreciableAmount = assetCost - salvageValue;

            if (method === "straight-line") {
                annualDepreciation = depreciableAmount / usefulLife;
            } else if (method === "declining-balance") {
                // Double declining balance (simplified)
                const rate = 2 / usefulLife;
                annualDepreciation = assetCost * rate;
            } else if (method === "sum-of-years") {
                // Sum of years digits (first year)
                const sumOfYears = (usefulLife * (usefulLife + 1)) / 2;
                annualDepreciation =
                    (depreciableAmount * usefulLife) / sumOfYears;
            }

            const totalDepreciation = Math.min(
                annualDepreciation * usefulLife,
                depreciableAmount,
            );
            const bookValue = assetCost - totalDepreciation;

            document.getElementById("annual-depreciation").textContent =
                "$" + annualDepreciation.toLocaleString();
            document.getElementById("total-depreciation").textContent =
                "$" + totalDepreciation.toLocaleString();
            document.getElementById("book-value").textContent =
                "$" + bookValue.toLocaleString();
        }

        // Estimated Tax Payment Calculator
        function calculateEstimatedTax() {
            const estimatedIncome =
                parseFloat(document.getElementById("estimated-income").value) ||
                0;
            const estimatedDeductions =
                parseFloat(
                    document.getElementById("estimated-deductions").value,
                ) || 0;
            const priorYearTax =
                parseFloat(document.getElementById("prior-year-tax").value) ||
                0;
            const withholdingCredits =
                parseFloat(
                    document.getElementById("withholding-credits").value,
                ) || 0;

            const taxableIncome = Math.max(
                0,
                estimatedIncome - estimatedDeductions,
            );
            const estimatedTaxLiability = calculateFederalTax(
                taxableIncome,
                "single",
            ); // Simplified
            const quarterlyPayment = Math.max(
                0,
                (estimatedTaxLiability - withholdingCredits) / 4,
            );
            const safeHarborAmount = (priorYearTax * 1.1) / 4; // 110% of prior year

            document.getElementById("estimated-tax-liability").textContent =
                "$" + estimatedTaxLiability.toLocaleString();
            document.getElementById("quarterly-payment").textContent =
                "$" + quarterlyPayment.toLocaleString();
            document.getElementById("safe-harbor-amount").textContent =
                "$" + safeHarborAmount.toLocaleString();
        }

        // Business Mileage Calculator
        function calculateMileage() {
            const businessMiles =
                parseFloat(document.getElementById("business-miles").value) ||
                0;
            const mileageRate =
                parseFloat(document.getElementById("mileage-rate").value) ||
                0.655;
            const totalMiles =
                parseFloat(document.getElementById("total-miles").value) || 0;

            const mileageDeduction = businessMiles * mileageRate;
            const businessPercentage =
                totalMiles > 0 ? (businessMiles / totalMiles) * 100 : 0;
            const annualMileageDeduction = mileageDeduction * 12; // Assuming monthly input

            document.getElementById("mileage-deduction").textContent =
                "$" + mileageDeduction.toLocaleString();
            document.getElementById("business-percentage").textContent =
                businessPercentage.toFixed(1) + "%";
            document.getElementById("annual-mileage-deduction").textContent =
                "$" + annualMileageDeduction.toLocaleString();
        }

        // Sales Tax Calculator
        function calculateSalesTax() {
            const saleAmount =
                parseFloat(document.getElementById("sale-amount").value) || 0;
            const taxRate =
                parseFloat(document.getElementById("tax-rate").value) || 0;
            const taxInclusive = document.getElementById("tax-inclusive").value;

            let netAmount, taxAmount, totalAmount;

            if (taxInclusive === "inclusive") {
                // Tax is included in the sale amount
                totalAmount = saleAmount;
                netAmount = saleAmount / (1 + taxRate / 100);
                taxAmount = saleAmount - netAmount;
            } else {
                // Tax is added to the sale amount
                netAmount = saleAmount;
                taxAmount = saleAmount * (taxRate / 100);
                totalAmount = saleAmount + taxAmount;
            }

            document.getElementById("net-amount").textContent =
                "$" + netAmount.toLocaleString();
            document.getElementById("tax-amount").textContent =
                "$" + taxAmount.toLocaleString();
            document.getElementById("sales-total-amount").textContent =
                "$" + totalAmount.toLocaleString();
        }

        // Cost of Capital Calculator
        function calculateCostOfCapital() {
            const debtAmount =
                parseFloat(document.getElementById("debt-amount").value) || 0;
            const debtRate =
                parseFloat(document.getElementById("debt-rate").value) || 0;
            const equityAmount =
                parseFloat(document.getElementById("equity-amount").value) || 0;
            const equityRate =
                parseFloat(document.getElementById("equity-rate").value) || 0;
            const taxRate =
                parseFloat(document.getElementById("tax-rate-wacc").value) || 0;

            const totalCapital = debtAmount + equityAmount;
            const debtWeight =
                totalCapital > 0 ? (debtAmount / totalCapital) * 100 : 0;
            const equityWeight =
                totalCapital > 0 ? (equityAmount / totalCapital) * 100 : 0;

            // WACC = (E/V * Re) + ((D/V * Rd) * (1 - T))
            const wacc =
                ((equityWeight / 100) * equityRate) / 100 +
                (((debtWeight / 100) * debtRate) / 100) * (1 - taxRate / 100);

            document.getElementById("total-capital").textContent =
                "$" + totalCapital.toLocaleString();
            document.getElementById("debt-weight").textContent =
                debtWeight.toFixed(1) + "%";
            document.getElementById("equity-weight").textContent =
                equityWeight.toFixed(1) + "%";
            document.getElementById("wacc").textContent =
                (wacc * 100).toFixed(2) + "%";
        }
    </script>
{% endblock %}
