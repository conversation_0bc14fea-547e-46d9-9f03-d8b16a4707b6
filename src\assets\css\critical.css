/*-- -------------------------- -->
<---           Hero             -->
<--- -------------------------- -*/
/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #hero-1467 {
    padding: clamp(13.5rem, 22.95vw, 16.875em) 1rem clamp(6.25rem, 7vw, 12.5rem);
    position: relative;
    z-index: 1;
  }
  #hero-1467 .cs-container {
    width: 100%;
    /* changes to 1280px at desktop */
    max-width: 44rem;
    margin: auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 3rem;
  }
  #hero-1467 .cs-content {
    text-align: center;
    width: 100%;
    max-width: 35.625rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    flex-direction: column;
    align-items: center;
  }
  #hero-1467 .cs-flex {
    text-align: center;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    flex-direction: column;
    align-items: center;
  }
  #hero-1467 .cs-title {
    /* 39px - 49px */
    font-size: clamp(2.4375rem, 6.4vw, 3.0625rem);
    font-weight: 700;
    line-height: 1.2em;
    text-align: inherit;
    max-width: 51.8125rem;
    /* 16px - 24px */
    margin: 0 auto clamp(1rem, 4vw, 1.5rem) 0;
    color: #fff;
    position: relative;
    z-index: 1;
  }
  #hero-1467 .cs-title:before {
    content: "";
    width: 3.75rem;
    height: 8px;
    margin: 0 auto 2rem;
    background: var(--primary);
    border-radius: 8px;
    opacity: 1;
    display: block;
  }
  #hero-1467 .cs-text {
    /* 16px - 20px */
    font-size: clamp(1rem, 1.95vw, 1.25rem);
    line-height: 1.5em;
    text-align: inherit;
    width: 100%;
    /* 464px - 622px */
    max-width: clamp(29rem, 60vw, 38.785rem);
    margin: 0;
    margin-bottom: 2rem;
    color: #fff;
  }
  #hero-1467 .cs-button-solid {
    font-size: 1rem;
    /* 46px - 56px */
    line-height: clamp(2.875rem, 5.5vw, 3.5rem);
    text-decoration: none;
    font-weight: 700;
    text-align: center;
    margin: 0;
    color: #fff;
    min-width: 12.5rem;
    padding: 0 1.5rem;
    background-color: var(--primary);
    border-radius: 3.125rem;
    overflow: hidden;
    display: inline-block;
    position: relative;
    z-index: 1;
    /* prevents padding from adding to the width */
    box-sizing: border-box;
  }
  #hero-1467 .cs-button-solid:before {
    content: "";
    position: absolute;
    height: 100%;
    width: 0%;
    background: #000;
    opacity: 1;
    top: 0;
    left: 0;
    z-index: -1;
    transition: width 0.3s;
  }
  #hero-1467 .cs-button-solid:hover:before {
    width: 100%;
  }
  #hero-1467 .cs-reviews {
    /* 40px - 80px */
    margin-top: clamp(2.5rem, 6vw, 5rem);
    /* prevents padding and border from affecting height and width */
    box-sizing: border-box;
    border-radius: 0.75rem;
    box-shadow: 0px 8px 100px 0px rgba(0, 0, 0, 0.08);
    display: inline-flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-direction: column;
    /* 8px - 16px */
    gap: clamp(0.5rem, 2vw, 1rem);
    position: relative;
    z-index: -1;
  }
  #hero-1467 .cs-reviews-flex {
    margin-bottom: 1rem;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  #hero-1467 .cs-stars {
    margin: 0 0 0.5rem 0;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2px;
  }
  #hero-1467 .cs-icon {
    width: 1rem;
    height: auto;
  }
  #hero-1467 .cs-desc {
    font-size: var(--bodyFontSize);
    line-height: 1.5em;
    text-align: inherit;
    max-width: 20ch;
    margin: 0;
    color: #f7f7f7;
  }
  #hero-1467 .cs-reviews-img {
    width: 3rem;
    height: auto;
    margin: 0 -1rem 0 0;
    border-radius: 50%;
    border: 1px solid #fff;
    background-color: #fff;
  }
  #hero-1467 .cs-reviews-number {
    font-size: 1rem;
    font-weight: 700;
    width: 3rem;
    height: 3rem;
    background-color: var(--secondary);
    color: var(--bodyTextColorWhite);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  #hero-1467 .cs-right {
    text-align: center;
    width: 100%;
    /* 16px - 48px left & right */
    /* 32px - 48px top & bottom */
    padding: clamp(2rem, 5vw, 3rem) clamp(1rem, 4vw, 3rem);
    box-sizing: border-box;
    border-radius: 1.5rem;
    border-top: 8px solid var(--primary);
    background-color: #fff;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;
  }
  #hero-1467 .cs-tag {
    font-size: 0.75rem;
    text-transform: uppercase;
    font-weight: 700;
    letter-spacing: 0.1em;
    line-height: 1.75rem;
    padding: 0 2rem 0 1.75rem;
    overflow: hidden;
    color: var(--bodyTextColorWhite);
    border-radius: 2.5rem;
    display: inline-block;
    position: absolute;
    top: 0.5rem;
    right: 0;
    z-index: 1;
    transform: rotate(14deg);
    transform-origin: right;
  }
  #hero-1467 .cs-tag:before {
    /* background color */
    content: "";
    height: 100%;
    background: var(--secondary);
    opacity: 1;
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    right: 20px;
    z-index: -1;
  }
  #hero-1467 .cs-tag:after {
    /* triangle */
    content: "";
    width: 1.5625rem;
    height: 1.5625rem;
    background-color: var(--secondary);
    opacity: 1;
    overflow: hidden;
    position: absolute;
    display: block;
    top: 50%;
    right: 0.375rem;
    z-index: -1;
    transform: translateY(-50%) rotate(45deg);
  }
  #hero-1467 .cs-dot {
    width: 0.5rem;
    height: 0.5rem;
    background-color: #fff;
    border-radius: 50%;
    position: absolute;
    right: 0.9375rem;
    top: 50%;
    transform: translateY(-50%);
  }
  #hero-1467 .cs-h2 {
    /* 31px - 39px */
    font-size: clamp(1.9375rem, 4vw, 2.4375rem);
    font-weight: 900;
    text-align: inherit;
    max-width: 20ch;
    margin: 0 0 0.5rem 0;
    color: var(--headerColor);
  }
  #hero-1467 .cs-expires {
    font-size: 1rem;
    line-height: 1.5em;
    text-align: inherit;
    margin: 0 0 1rem 0;
    color: var(--secondary);
    display: block;
  }
  #hero-1467 .cs-form-text {
    font-size: var(--bodyFontSize);
    line-height: 1.5em;
    text-align: inherit;
    width: 100%;
    max-width: 40.625rem;
    margin: 0 0 1.5rem 0;
    color: var(--bodyTextColor);
  }
  #hero-1467 .cs-form {
    width: 100%;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    align-items: center;
    gap: 1rem;
  }
  #hero-1467 .cs-label {
    font-size: 0.875rem;
    width: 100%;
    color: #767676;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 0.25rem;
  }
  #hero-1467 .cs-input,
  #hero-1467 select {
    font-size: 1rem;
    width: 100%;
    height: 3.5rem;
    padding: 0;
    padding-left: 1.5rem;
    color: var(--bodyTextColor);
    background-color: #f7f7f7;
    border: none;
    border-radius: 0.5rem;
    /* prevents padding from adding to height and width */
    box-sizing: border-box;
    transition: border-color 0.3s;
  }
  #hero-1467 .cs-input:hover,
  #hero-1467 select:hover {
    border-color: var(--secondary);
  }
  #hero-1467 .cs-input:focus,
  #hero-1467 select:focus {
    outline: 1px solid var(--secondary);
  }
  #hero-1467 .cs-input::placeholder,
  #hero-1467 select::placeholder {
    color: #7d799c;
    opacity: 0.6;
  }
  #hero-1467 .cs-textarea {
    min-height: 5.9375rem;
    padding-top: 1.5rem;
    margin-bottom: 0.75rem;
    font-family: inherit;
  }
  #hero-1467 .cs-submit {
    text-transform: uppercase;
    width: 100%;
    margin: 0 0 1rem 0;
    border: none;
  }
  #hero-1467 .cs-submit:hover {
    cursor: pointer;
  }
  #hero-1467 .cs-disclaimer {
    font-size: 0.875rem;
    text-align: left;
    line-height: 1.5em;
    margin: 0 auto 0 0;
    color: var(--bodyTextColor);
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.25rem;
  }
  #hero-1467 .cs-disclaimer-icon {
    width: 1.25rem;
    height: auto;
  }
  #hero-1467 .cs-background {
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -2;
  }
  #hero-1467 .cs-background:before {
    /* Overlay */
    content: "";
    width: 100%;
    height: 100%;
    background: #1a1a1a;
    opacity: 0.88;
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    z-index: 1;
    /* prevents the cursor from interacting with it */
    pointer-events: none;
  }
  #hero-1467 .cs-background img {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    object-fit: cover;
  }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #hero-1467 .cs-form {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
  }
  #hero-1467 .cs-label {
    grid-column: span 12;
  }
  #hero-1467 .cs-label:nth-of-type(1), #hero-1467 .cs-label:nth-of-type(2), #hero-1467 .cs-label:nth-of-type(3), #hero-1467 .cs-label:nth-of-type(4) {
    grid-column: span 6;
  }
  #hero-1467 .cs-submit {
    grid-column: span 12;
  }
}
/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
  #hero-1467 .cs-container {
    max-width: 80rem;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
  }
  #hero-1467 .cs-right {
    align-items: flex-start;
  }
  #hero-1467 .cs-title:before {
    margin-left: 0;
  }
  #hero-1467 .cs-content {
    text-align: left;
    align-items: flex-start;
  }
  #hero-1467 .cs-flex {
    text-align: left;
    align-items: flex-start;
  }
}
/* Desktop Parallax Effect - 1600px */
@media only screen and (min-width: 100rem) {
  #hero-1467 {
    background: url("/assets/images/hero.webp");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
  }
  #hero-1467 .cs-background img {
    display: none;
  }
}
/* Dark Mode */
@media only screen and (min-width: 0rem) {
  body.dark-mode #hero-1467 .cs-right {
    background-color: var(--medium);
  }
  body.dark-mode #hero-1467 .cs-h2,
  body.dark-mode #hero-1467 .cs-form-text,
  body.dark-mode #hero-1467 .cs-label,
  body.dark-mode #hero-1467 .cs-disclaimer {
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #hero-1467 .cs-disclaimer-icon {
    /* turns it white */
    filter: grayscale(1) brightness(1000%);
  }
  body.dark-mode #hero-1467 .cs-input,
  body.dark-mode #hero-1467 select {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #hero-1467 .cs-form-text {
    opacity: 0.8;
  }
}

/*# sourceMappingURL=critical.css.map */
