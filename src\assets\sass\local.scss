/*-- -------------------------- -->
<---          Services          -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #services-1303 {
        padding: var(--sectionPadding);
        position: relative;
        z-index: 10;

        .cs-container {
            width: 100%;
            max-width: calc(1280 / 16 * 1rem);
            margin: auto;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            /* 48px - 64px */
            gap: clamp(3rem, 6vw, 4rem);
        }

        .cs-content {
            /* set text align to left if content needs to be left aligned */
            text-align: left;
            width: 100%;
            display: flex;
            flex-direction: column;
            /* centers content horizontally, set to flex-start to left align */
            align-items: flex-start;
        }

        .cs-title {
            max-width: 20ch;
        }

        .cs-card-group {
            width: 100%;
            max-width: calc(1280 / 16 * 1rem);
            margin: 0;
            padding: 0 calc(16 / 16 * 1rem);
            /* prevents padding and border from affecting height and width */
            background-color: #fff;
            box-shadow: 0px 4px 35px 0px rgba(0, 0, 0, 0.05);
            box-sizing: border-box;
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            position: relative;
            z-index: 15;
        }

        .cs-item {
            text-align: left;
            list-style: none;
            width: 100%;
            border-bottom: 1px solid #e8e8e8;
            box-sizing: border-box;
            grid-column: span 12;
            transition:
                background-color 0.3s,
                border-color 0.3s;

            &:last-of-type {
                border-bottom: none;
            }
        }

        .cs-link {
            text-decoration: none;
            /* 24px - 60px top & Bottom */
            /* 24px - 16px top & Bottom */
            padding: clamp(1.5rem, 5.3vw, 3.75rem) clamp(1rem, 2.7vw, 2.5rem);
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-direction: column;
        }

        .cs-icon {
            /* 32px - 40px */
            height: clamp(2rem, 4vw, 2.5rem);
            width: auto;
            margin: 0 0 calc(16 / 16 * 1rem) 0;
        }

        .cs-h3 {
            /* 20px - 25px */
            font-size: clamp(1.25rem, 2vw, 1.5625rem);
            font-weight: 700;
            text-align: inherit;
            line-height: 1.2em;
            margin: 0 0 calc(12 / 16 * 1rem) 0;
            color: var(--headerColor);
            transition: color 0.3s;
        }

        .cs-item-text {
            /* 14px - 16px */
            font-size: clamp(0.875rem, 1.5vw, 1rem);
            text-align: inherit;
            line-height: 1.5em;
            margin: 0;
            color: var(--bodyTextColor);
            transition:
                color 0.3s,
                opacity 0.3s;
        }
    }
}

/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
    #services-1303 {
        .cs-content {
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            gap: calc(48 / 16 * 1rem);
        }

        .cs-title {
            margin: 0;
        }

        .cs-flex-group {
            width: 50%;
            /* prevents flexbox from squishing it */
            flex: none;
        }

        .cs-item {
            grid-column: span 4;
            border-right: 1px solid #e8e8e8;

            &:nth-of-type(3),
            &:nth-of-type(6) {
                border-right: none;
            }

            &:nth-of-type(4),
            &:nth-of-type(5) {
                border-bottom: none;
            }
        }
    }
}

/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
    #services-1303 {
        .cs-card-group {
            padding: 0;
        }

        .cs-item {
            &:hover {
                background-color: #1a1a1a;
                border-color: #1a1a1a;

                .cs-h3,
                .cs-item-text {
                    color: var(--bodyTextColorWhite);
                }

                .cs-item-text {
                    opacity: 0.8;
                }
            }

            &:last-of-type {
                border: none;
            }
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        #services-1303 {

            .cs-title,
            .cs-text {
                color: var(--bodyTextColorWhite);
            }

            .cs-text {
                opacity: 0.8;
            }

            .cs-card-group {
                background-color: rgba(0, 0, 0, 0.3);
            }

            .cs-item {
                border-color: rgba(255, 255, 255, 0.1);

                &:hover {
                    background-color: rgba(0, 0, 0, 0.6);
                    border-color: rgba(0, 0, 0, 0.6);
                }
            }

            .cs-h3,
            .cs-item-text {
                color: var(--bodyTextColorWhite);
            }

            .cs-item-text {
                opacity: 0.8;
            }
        }
    }
}

/*-- -------------------------- -->
<---            FAQ             -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #faq-1109 {
        padding: var(--sectionPadding);
        position: relative;

        .cs-container {
            width: 100%;
            /* changes to 1280px at desktop */
            max-width: calc(584 / 16 * 1rem);
            margin: auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            /* 32px - 48px */
            gap: clamp(2rem, 6vw, 3rem);
        }

        .cs-content {
            /* set text align to left if content needs to be left aligned */
            text-align: center;
            width: 100%;
            display: flex;
            flex-direction: column;
            /* centers content horizontally, set to flex-start to left align */
            align-items: center;
        }

        .cs-title {
            /* 32px - 48px */
            margin: 0 0 clamp(2rem, 5vw, 3rem);
        }

        .cs-picture {
            width: 100%;
            /* 360px - 400px */
            height: clamp(22.5rem, 54vw, 25rem);
            display: block;
            position: relative;
            overflow: hidden;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .cs-faq-group {
            width: 100%;
            max-width: calc(650 / 16 * 1rem);
            padding: 0;
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            gap: calc(12 / 16 * 1rem);
        }

        .cs-faq-item {
            list-style: none;
            width: 100%;
            background-color: #f7f7f7;
            /* clips all corners of the button that overlap the rounded border */
            overflow: hidden;
            transition: border-bottom 0.3s;

            &.active {
                .cs-button {
                    color: var(--primary);

                    &:before {
                        background-color: var(--primary);
                        transform: rotate(315deg);
                    }

                    &:after {
                        background-color: var(--primary);
                        transform: rotate(-315deg);
                    }
                }

                .cs-item-p {
                    height: auto;
                    /* 20px - 24px bottom */
                    /* 16px - 24px left & right */
                    padding: 0 clamp(1rem, 2vw, 1.5rem) clamp(1.25rem, 1.3vw, 1.5rem);
                    opacity: 1;
                }
            }
        }

        .cs-button {
            /* 16px - 20px */
            font-size: clamp(1rem, 2.5vw, 1.25rem);
            line-height: 1.2em;
            text-align: left;
            font-weight: bold;
            /* 20px - 24px */
            padding: clamp(1.25rem, 2vw, 1.5rem);
            background-color: #f7f7f7;
            border: none;
            color: var(--headerColor);
            display: block;
            width: 100%;
            position: relative;
            transition:
                background-color 0.3s,
                color 0.3s;

            &:hover {
                cursor: pointer;
            }

            &:before {
                /* left line */
                content: "";
                width: calc(8 / 16 * 1rem);
                height: calc(2 / 16 * 1rem);
                background-color: var(--headerColor);
                opacity: 1;
                border-radius: 50%;
                position: absolute;
                display: block;
                top: 50%;
                right: calc(24 / 16 * 1rem);
                transform: rotate(45deg);
                /* animate the transform from the left side of the x axis, and the center of the y */
                transform-origin: left center;
                transition: transform 0.5s;
            }

            &:after {
                /* right line */
                content: "";
                width: calc(8 / 16 * 1rem);
                height: calc(2 / 16 * 1rem);
                background-color: var(--headerColor);
                opacity: 1;
                border-radius: 50%;
                position: absolute;
                display: block;
                top: 50%;
                right: calc(21 / 16 * 1rem);
                transform: rotate(-45deg);
                /* animate the transform from the right side of the x axis, and the center of the y */
                transform-origin: right center;
                transition: transform 0.5s;
            }
        }

        .cs-button-text {
            width: 80%;
            display: block;
        }

        .cs-item-p {
            /* 14px - 16px */
            font-size: clamp(0.875rem, 1.5vw, 1rem);
            line-height: 1.5em;
            width: 90%;
            height: 0;
            margin: 0;
            /* 16px - 24px */
            padding: 0 clamp(1rem, 2vw, 1.5rem);
            opacity: 0;
            color: var(--bodyTextColor);
            /* clips the text so it doesn't show up */
            overflow: hidden;
            transition:
                opacity 0.3s,
                padding-bottom 0.3s;
        }
    }
}

/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
    #faq-1109 {
        .cs-container {
            max-width: calc(1280 / 16 * 1rem);
            flex-direction: row;
            justify-content: space-between;
            align-items: stretch;
        }

        .cs-content {
            width: 40%;
            text-align: left;
            align-items: flex-start;
            /* prevents flexbox from squishing it */
            flex: none;
            /* sends it to the right in the 2nd position */
            order: 2;
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        #faq-1109 {

            .cs-title,
            .cs-item-p {
                color: var(--bodyTextColorWhite);
            }

            .cs-faq-item {
                background-color: var(--accent);

                &.active {
                    .cs-button {
                        background-color: var(--primary);
                        color: var(--bodyTextColorWhite);

                        &:before,
                        &:after {
                            background-color: var(--bodyTextColorWhite);
                        }
                    }

                    .cs-item-p {
                        /* 20px - 24px */
                        padding-top: clamp(1.25rem, 1.3vw, 1.5rem);
                    }
                }
            }

            .cs-button {
                background-color: var(--accent);
                color: var(--bodyTextColorWhite);

                &:before,
                &:after {
                    background-color: var(--bodyTextColorWhite);
                }
            }
        }
    }
}

/*-- -------------------------- -->
<---          Services          -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #services-1355 {
        padding: var(--sectionPadding);
        position: relative;
        z-index: 10;

        .cs-container {
            width: 100%;
            max-width: calc(1280 / 16 * 1rem);
            margin: auto;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            gap: clamp(3rem, 6vw, 4rem);
        }

        .cs-content {
            /* set text align to left if content needs to be left aligned */
            text-align: center;
            width: 100%;
            display: flex;
            flex-direction: column;
            /* centers content horizontally, set to flex-start to left align */
            align-items: center;
        }

        .cs-topper {
            color: var(--primary);
            margin: auto;
        }

        .cs-title {
            max-width: 25ch;
            margin: 0 auto;
            text-align: center;
        }

        .cs-card-group {
            margin: 0;
            padding: 0;
            width: 100%;
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            /* 16px - 20px */
            gap: clamp(1rem, 2vw, 1.25rem);
        }

        .cs-item {
            text-align: center;
            list-style: none;
            width: 100%;
            height: calc(305 / 16 * 1rem);
            margin: 0;
            padding: 0;
            background-color: #000;
            border-radius: calc(24 / 16 * 1rem);
            /* clips background image corners */
            overflow: hidden;
            box-shadow: 0px 12px 80px 0px rgba(26, 26, 26, 0.08);
            /* prevents padding and border from affecting height and width */
            box-sizing: border-box;
            grid-column: span 12;
            grid-row: span 1;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: flex-end;
            position: relative;
            z-index: 1;

            &:hover {
                .cs-background {
                    &:before {
                        background-color: var(--primary);
                        opacity: 0.84;
                    }

                    img {
                        transform: scale(1.2);
                    }
                }
            }
        }

        .cs-link {
            text-decoration: none;
            width: 100%;
            height: 100%;
            /* padding goes on the link, not the cs-item as is normal. We do this because we want the whole card to be hoverable. So we add the padding to the link tag to create the space inside the card. By adding the space inside the cs-link tag we can make the whole card hoverable since the padding is now contributing to the height and widht of the link */
            padding: calc(24 / 16 * 1rem);
            /* prevents padding from affecting height and width */
            box-sizing: border-box;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .cs-h3 {
            /* 20px - 25px */
            font-size: clamp(1.25rem, 2.5vw, 1.5625rem);
            line-height: 1.2em;
            font-weight: bold;
            text-align: inherit;
            margin: 0;
            color: var(--bodyTextColorWhite);
            transition: color 0.3s;
        }

        .cs-span {
            /* forces the h3 to alwasy be two lines */
            display: block;
        }

        .cs-background {
            width: 100%;
            height: 100%;
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            z-index: -1;

            &:before {
                /* background color overlay */
                content: "";
                position: absolute;
                display: block;
                height: 100%;
                width: 100%;
                background-color: #000;
                opacity: 0.4;
                top: 0;
                left: 0;
                z-index: 1;
                transition:
                    background-color 0.3s,
                    opacity 0.3s;
            }

            img {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                /* Makes img tag act as a background image */
                object-fit: cover;
                transition: transform 0.6s;
            }
        }
    }
}

/* Tablet - 600px */
@media only screen and (min-width: 37.5rem) {
    #services-1355 {
        .cs-content {
            text-align: left;
            align-items: flex-start;
        }

        .cs-item {
            grid-column: span 4;
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        #services-1355 {
            .cs-topper {
                color: var(--primary);
            }

            .cs-title {
                color: var(--bodyTextColorWhite);
            }
        }
    }
}